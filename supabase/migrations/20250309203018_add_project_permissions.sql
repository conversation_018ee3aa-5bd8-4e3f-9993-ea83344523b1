create type "public"."project_roles" as enum('viewer', 'editor', 'owner');

-- Project Permission Table - User permissions and access control for project
create table "public"."project_permission" (
	project_permission_id bigint generated always as identity primary key,
	project_id uuid not null references project (project_id) on update restrict on delete restrict,
	user_id uuid not null references profile (user_id) on update restrict on delete restrict,
	role public.project_roles not null,
	created_at timestamptz default timezone ('utc'::text, now()) not null,
	updated_at timestamptz default timezone ('utc'::text, now()) not null,
	unique (project_id, user_id)
);

comment on table "public"."project_permission" is 'User permissions and access control for project';

-- Create trigger to update the updated_at timestamp
create trigger update_updated_at before
update on public.project_permission for each row
execute function public.update_updated_at_column ();

-- auth.uid() to add creator as project owner
create function public.add_owner_to_project_permission () returns trigger
set
	search_path = '' as $$ begin
insert into public.project_permission (project_id, user_id, role)
values (
		new.project_id,
		new.created_by_user_id,
		'owner'
	);
return new;
end;
$$ language plpgsql security definer;

comment on function public.add_owner_to_project_permission () is 'Automatically adds the project creator as an owner in project permission';

-- Grant execute to service_role
grant
execute on function public.add_owner_to_project_permission () to service_role;

-- Create trigger to automatically add creator as project owner
create trigger add_owner_to_project_permission
after insert on public.project for each row
execute function public.add_owner_to_project_permission ();

-- Enable Row Level Security
alter table project_permission enable row level security;

-- Grant access to service_role
grant
select
,
	insert,
update,
delete,
references,
trigger,
truncate on public.project_permission to service_role;

-- Create a security definer function to check if a user has access to a specific project
create function public.can_access_project (project_id_param uuid) returns boolean
set
	search_path = '' as $$ begin return exists (
	-- User has explicit project permission
	select 1
	from public.project_permission pp
	where pp.user_id = (
			select auth.uid()
		)
		and pp.project_id = project_id_param
)
or exists (
	-- User is an organization member for the client's organization
	select 1
	from public.project p
		join public.client c on p.client_id = c.client_id
		join public.organization_member om on c.org_id = om.org_id
	where p.project_id = project_id_param
		and c.client_id = p.client_id
		and om.user_id = (
			select auth.uid()
		)
)
or exists (
	-- User has explicit client permission
	select 1
	from public.project p
		join public.client c on p.client_id = c.client_id
		join public.client_permission cp on c.client_id = cp.client_id
	where p.project_id = project_id_param
		and cp.user_id = (
			select auth.uid()
		)
);
end;
$$ language plpgsql security definer;

comment on function public.can_access_project (uuid) is 'Check if a user has access to a specific project';

-- Grant execute to authenticated users
grant
execute on function public.can_access_project (uuid) to authenticated;

-- Create a security definer function to check if a user can modify a project
create function public.can_modify_project (project_id_param uuid) returns boolean
set
	search_path = '' as $$ begin return exists (
	select 1
	from public.project_permission pp
	where pp.project_id = project_id_param
		and pp.user_id = (
			select auth.uid()
		)
		and pp.role in ('editor', 'owner')
)
or exists (
	-- user is a client editor or admin
	select 1
	from public.project p
		join public.client c on p.client_id = c.client_id
		join public.client_permission cp on c.client_id = cp.client_id
	where p.project_id = project_id_param
		and cp.user_id = (
			select auth.uid()
		)
		and cp.role in ('editor', 'admin')
)
or exists (
	-- user is an admin for the client's organization
	select 1
	from public.project p
		join public.client c on p.client_id = c.client_id
		join public.organization_member om on c.org_id = om.org_id
	where p.project_id = project_id_param
		and om.user_id = (
			select auth.uid()
		)
		and om.role = 'admin'
);
end;
$$ language plpgsql security definer;

comment on function public.can_modify_project (uuid) is 'Check if a user can modify a project';

-- Grant execute to authenticated users
grant
execute on function public.can_modify_project (uuid) to authenticated;

-- Create a security definer function to check if a user is an organization admin for a project
create function public.is_org_admin_for_project (project_id_param uuid) returns boolean
set
	search_path = '' as $$ begin return exists (
	select 1
	from public.project p
		join public.client c on p.client_id = c.client_id
		join public.organization_member om on c.org_id = om.org_id
	where p.project_id = project_id_param
		and om.user_id = (
			select auth.uid()
		)
		and om.role = 'admin'
);
end;
$$ language plpgsql security definer;

comment on function public.is_org_admin_for_project (uuid) is 'Check if the current user is an organization admin for the project';

-- Grant execute to service_role only
grant
execute on function public.is_org_admin_for_project (uuid) to service_role;

-- Create a security definer function to check if a user is a project owner
create function public.is_project_owner (project_id_param uuid) returns boolean
set
	search_path = '' as $$ begin return exists (
	select 1
	from public.project_permission pp
	where pp.project_id = project_id_param
		and pp.user_id = (
			select auth.uid()
		)
		and pp.role = 'owner'
);
end;
$$ language plpgsql security definer;

comment on function public.is_project_owner (uuid) is 'Check if the current user is a project owner';

-- Grant execute to authenticated users
grant
execute on function public.is_project_owner (uuid) to authenticated;

-- Create a security definer function to check if a user is a project editor
create function public.is_project_editor (project_id_param uuid) returns boolean
set
	search_path = '' as $$ begin return exists (
	select 1
	from public.project_permission pp
	where pp.project_id = project_id_param
		and pp.user_id = auth.uid()
		and pp.role = 'editor'
);
end;
$$ language plpgsql security definer;

comment on function public.is_project_editor (uuid) is 'Check if the current user is a project editor';

-- Grant execute to authenticated users
grant
execute on function public.is_project_editor (uuid) to authenticated;

-- Create a security definer function to check if a user can access a client
create function public.can_access_client (client_id_param uuid) returns boolean
set
	search_path = '' as $$ begin return exists (
	-- User is an organization member for the client's organization
	select 1
	from public.client c
		join public.organization_member om on c.org_id = om.org_id
	where c.client_id = client_id_param
		and om.user_id = (
			select auth.uid()
		)
)
or exists (
	-- User has explicit client permission
	select 1
	from public.client_permission cp
	where cp.client_id = client_id_param
		and cp.user_id = (
			select auth.uid()
		)
);
end;
$$ language plpgsql;

comment on function public.can_access_client (uuid) is 'Check if the current user can access a client';

-- Grant execute to authenticated users
grant
execute on function public.can_access_client (uuid) to authenticated;

-- Setup policies
-- Anyone who can view a client can create project for that client
create policy "Client viewers can create project" on public.project for insert to authenticated
with
	check (
		public.current_user_has_entity_access ('client', project.client_id)
		and project.created_by_user_id = auth.uid ()
	);

-- Project policies
create policy "Users with project access can view project" on public.project for
select
	to authenticated using (
		public.current_user_has_entity_access ('project', project_id)
	);

-- Project Editors and Owners can update project
create policy "Project Editors and Owners can update project" on public.project
for update
	to authenticated using (
		public.current_user_has_entity_role ('project', project_id, 'editor')
	);

-- Project Owners can delete project
create policy "Project Owners can delete project" on public.project for delete to authenticated using (
	public.current_user_has_entity_role ('project', project_id, 'owner')
	or public.current_user_has_entity_role (
		'organization',
		(
			select
				c.org_id
			from
				client c
				join project p on p.client_id = c.client_id
			where
				p.project_id = project_id
		),
		'admin'
	)
);

-- Project Owners can add project permission
create policy "Project Owners can insert project permission" on public.project_permission for insert to authenticated
with
	check (
		public.is_project_owner (project_id)
		or public.is_org_admin_for_project (project_id)
	);

-- Project Owners can update project permission
create policy "Project Owners can update project permission" on public.project_permission
for update
	to authenticated using (
		public.is_project_owner (project_id)
		or public.is_org_admin_for_project (project_id)
	);

-- Project Owners can delete project permission
create policy "Project Owners can delete project permission" on public.project_permission for delete to authenticated using (
	public.is_project_owner (project_id)
	or public.is_org_admin_for_project (project_id)
);

-- Project users can view project permission
create policy "Project users can view project permission" on public.project_permission for
select
	to authenticated using (public.can_access_project (project_id));

-- Create trigger to add creator as owner
create trigger add_creator_as_admin_project
after insert on public.project for each row
execute function public.add_creator_as_admin ();
