create type "public"."client_roles" as enum('viewer', 'editor', 'admin');

-- Client Permission Table - User permissions and access control for client
create table "public"."client_permission" (
	client_permission_id bigint generated always as identity primary key,
	client_id uuid not null references client (client_id) on update restrict on delete restrict,
	user_id uuid not null references profile (user_id) on update restrict on delete restrict,
	role public.client_roles not null,
	created_at timestamptz default timezone ('utc'::text, now()) not null,
	updated_at timestamptz default timezone ('utc'::text, now()) not null,
	unique (client_id, user_id)
);

comment on table "public"."client_permission" is 'User permissions and access control for clients';

-- Create trigger to update the updated_at timestamp
create trigger update_updated_at before
update on public.client_permission for each row
execute function public.update_updated_at_column ();

-- Enable Row Level Security
alter table client_permission enable row level security;

-- Grant access to service_role
grant
select
,
	insert,
update,
delete,
references,
trigger,
truncate on public.client_permission to service_role;

-- Create a security definer function to check if a user is a client admin
create function public.is_client_admin (client_id_param uuid) returns boolean
set
	search_path = '' as $$ begin return public.current_user_has_entity_role('client', client_id_param, 'admin');
end;
$$ language plpgsql security definer;

comment on function public.is_client_admin (uuid) is 'Check if a user is an admin for a specific client';

grant
execute on function public.is_client_admin (uuid) to authenticated;

-- Setup policies
-- Organization admins can add client permission
create policy "Organization admins can insert client permission" on public.client_permission for insert to authenticated
with
	check (public.is_client_admin (client_id));

-- Organization admins and client admins can update client permission
create policy "Admins can update client permission" on public.client_permission
for update
	to authenticated using (public.is_client_admin (client_id));

-- Organization admins and client admins can delete client permission
create policy "Admins can delete client permission" on public.client_permission for delete to authenticated using (public.is_client_admin (client_id));

-- Create a security definer function to check user permissions
create function public.check_user_client_permissions (user_id_param uuid) returns boolean
set
	search_path = '' as $$ begin return exists (
	select 1
	from public.membership m
	where m.user_id = user_id_param
		and m.entity_type = 'client'
);
end;
$$ language plpgsql security definer;

comment on function public.check_user_client_permissions (uuid) is 'Check if a user has any client permissions';

-- Grant execute to authenticated users
grant
execute on function public.check_user_client_permissions (uuid) to authenticated;

-- Organization members and users with client permissions can view client permission
create policy "Users with permissions can view client permission" on public.client_permission for
select
	to authenticated using (
		public.check_user_client_permissions (
			(
				select
					auth.uid ()
			)
		)
	);

-- Client policies
create policy "Members can view clients" on public.client for
select
	to authenticated using (
		public.current_user_has_entity_access ('client', client_id)
	);

create policy "Client editors can update clients" on public.client
for update
	to authenticated using (
		public.current_user_has_entity_role ('client', client_id, 'editor')
	)
with
	check (
		public.current_user_has_entity_role ('client', client_id, 'editor')
	);

create policy "Client admins can delete clients" on public.client for delete to authenticated using (
	public.current_user_has_entity_role ('client', client_id, 'admin')
);

-- Create trigger to add creator as admin
create trigger add_creator_as_admin_client
after insert on public.client for each row
execute function public.add_creator_as_admin ();
