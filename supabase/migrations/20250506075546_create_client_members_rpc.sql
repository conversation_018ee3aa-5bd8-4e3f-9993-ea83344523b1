create or replace function profiles_with_client_access (_client_name text) returns table (
	user_id uuid,
	email text,
	full_name text,
	avatar_url text,
	created_at timestamptz,
	updated_at timestamptz,
	role text,
	membership_id bigint,
	access_via text
) language sql stable security definer
set
	search_path = '' as $$
	/* ── via ORGANIZATION membership ── */
select p.user_id,
	p.email,
	p.full_name,
	p.avatar_url,
	p.created_at,
	p.updated_at,
	m.role::text as role,
	m.membership_id,
	'organization'::text as access_via
from public.profile p
	join public.membership m on p.user_id = m.user_id
	join public.client c on m.entity_type = 'organization'
	and m.entity_id = c.org_id
where c.name = _client_name
union all
/* ── via CLIENT membership ── */
select p.user_id,
	p.email,
	p.full_name,
	p.avatar_url,
	p.created_at,
	p.updated_at,
	m.role::text as role,
	m.membership_id,
	'client'::text as access_via
from public.profile p
	join public.membership m on p.user_id = m.user_id
	join public.client c on m.entity_type = 'client'
	and m.entity_id = c.client_id
where c.name = _client_name
order by created_at desc;
$$;

-- Only allow authenticated users to execute the function
grant
execute on function profiles_with_client_access (text) to authenticated;

grant
execute on function profiles_with_client_access (text) to service_role;

-- Create a wrapper function that only client admins can use
create or replace function get_client_members (_client_name text) returns table (
	user_id uuid,
	email text,
	full_name text,
	avatar_url text,
	created_at timestamptz,
	updated_at timestamptz,
	role text,
	membership_id bigint,
	access_via text
) language plpgsql security definer
set
	search_path = '' as $$
declare _client_id uuid;
_has_admin_access boolean;
begin -- Get the client_id for the client name
select client_id into _client_id
from public.client
where name = _client_name;
if _client_id is null then raise exception 'Client not found: %',
_client_name;
end if;
-- Check if the current user has admin access to the client
select public.current_user_has_entity_role('client', _client_id, 'admin') into _has_admin_access;
if not _has_admin_access then raise exception 'Access denied: must be a client admin';
end if;
-- Return client members if user is an admin
return query
select *
from public.profiles_with_client_access(_client_name);
end;
$$;

-- Only allow authenticated users to execute the function
grant
execute on function get_client_members (text) to authenticated;

grant
execute on function get_client_members (text) to service_role;
