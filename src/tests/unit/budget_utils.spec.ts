import { describe, it, expect } from 'vitest';
import { buildBudgetTree, calculateUnitRate, type RawNode } from '$lib/budget_utils';

// Helper function to build a RawNode with minimal fields
function makeRawNode(
	id: number,
	parentId: number | null,
	quantity: number,
	unitRate: number,
): RawNode {
	return {
		client_id: null,
		code: `code-${id}`,
		cost_scope: null,
		created_at: '',
		description: `Node ${id}`,
		in_level_code: `node-${id}`,
		item_type: 'Standard',
		level: parentId ? 2 : 1,
		parent_item_id: parentId,
		project_id: null,
		updated_at: '',
		wbs_library_id: 1,
		wbs_library_item_id: id,
		budget_line_item_current: [
			{
				budget_line_item_id: id,
				cost_certainty: null,
				created_at: '',
				design_certainty: null,
				labor_rate: null,
				material_rate: unitRate,
				productivity_per_hour: null,
				project_id: 'proj',
				quantity,
				remarks: null,
				unit: null,
				unit_rate: unitRate,
				unit_rate_manual_override: false,
				updated_at: '',
				wbs_library_item_id: id,
			},
		],
	} as unknown as RawNode;
}

describe('calculateUnitRate', () => {
	it('returns manual unit rate when override flag is set', () => {
		const item = { unit_rate_manual_override: true, unit_rate: 12 } as any;
		expect(calculateUnitRate(item)).toBe(12);
	});

	it('defaults missing manual unit rate to zero', () => {
		const item: any = { unit_rate_manual_override: true };
		expect(calculateUnitRate(item)).toBe(0);
		expect(item.unit_rate).toBe(0);
	});

	it('calculates rate from material and labor', () => {
		const item = { material_rate: 10, labor_rate: 20, productivity_per_hour: 4 } as any;
		expect(calculateUnitRate(item)).toBe(10 + 20 / 4);
	});
});

describe('buildBudgetTree', () => {
	it('computes direct, children and total costs', () => {
		const raw = [makeRawNode(1, null, 2, 10), makeRawNode(2, 1, 3, 5)];
		const tree = buildBudgetTree(raw);
		expect(tree).toHaveLength(1);
		const root = tree[0];
		expect(root.directCost).toBe(20); // 2 * 10
		expect(root.children).toHaveLength(1);
		const child = root.children[0];
		expect(child.totalCost).toBe(15); // 3 * 5
		expect(root.childrenCost).toBe(child.totalCost);
		expect(root.totalCost).toBe(root.directCost + child.totalCost);
	});
});
