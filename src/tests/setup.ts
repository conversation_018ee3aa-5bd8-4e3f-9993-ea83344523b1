// @ts-nocheck
import { expect, vi, beforeAll, afterEach, afterAll } from 'vitest';
import {
	toBeInTheDocument,
	toHaveAttribute,
	toHaveTextContent,
} from '@testing-library/jest-dom/matchers';

// Add custom matchers to Vitest's expect
expect.extend({
	toBeInTheDocument,
	toHaveAttribute,
	toHaveTextContent,
});

// Set up a more complete mock DOM environment
global.document = {
	body: {},
	querySelector: vi.fn(() => ({
		getAttribute: () => null,
	})),
	createElement: vi.fn(() => ({
		setAttribute: vi.fn(),
		appendChild: vi.fn(),
		classList: {
			add: vi.fn(),
			remove: vi.fn(),
		},
	})),
} as any;

global.window = {
	document: global.document,
	location: { href: 'http://localhost/' },
	addEventListener: vi.fn(),
	removeEventListener: vi.fn(),
} as any;

// Mock environment variables
vi.mock('$env/static/public', () => ({
	PUBLIC_SUPABASE_URL: 'https://example.supabase.co',
	PUBLIC_SUPABASE_ANON_KEY: 'mock-anon-key',
}));

vi.mock('$env/static/private', () => ({
	SUPABASE_SERVICE_KEY: 'mock-service-key',
}));

// Mock SvelteKit functions
vi.mock('$app/navigation', () => ({
	goto: vi.fn(),
	invalidate: vi.fn(),
}));

vi.mock('$app/forms', () => ({
	enhance: vi.fn(() => ({ destroy: vi.fn() })),
}));

// Mock BROWSER value that's used in SvelteKit
vi.mock('@sveltejs/kit', async (importOriginal) => {
	return {
		BROWSER: false, // Set to false to avoid window reference issues
		redirect: vi.fn().mockImplementation((status, location) => {
			const error = new Error(`Redirect to ${location}`);
			error.status = status;
			error.location = location;
			throw error;
		}),
		fail: vi.fn((status, data) => ({ status, data })),
	};
});

vi.mock('$app/stores', () => {
	const getStores = vi.fn(() => ({
		navigating: { subscribe: vi.fn() },
		page: {
			subscribe: vi.fn(),
			url: new URL('http://localhost:5173'),
			params: {},
			route: { id: '/' },
		},
		updated: { subscribe: vi.fn() },
	}));

	return {
		getStores,
		page: {
			subscribe(fn: (value: any) => void) {
				fn(getStores().page);
				return () => {};
			},
		},
		navigating: {
			subscribe(fn: (value: any) => void) {
				fn(null);
				return () => {};
			},
		},
	};
});

// Mock sveltekit-superforms
vi.mock('sveltekit-superforms', () => ({
	superForm: vi.fn(() => ({
		form: {},
		errors: {},
		constraints: {},
		message: { subscribe: vi.fn() },
		enhance: vi.fn(),
		reset: vi.fn(),
		submit: vi.fn(),
		delayed: { subscribe: vi.fn() },
		submitting: { subscribe: vi.fn() },
	})),
}));

// Mock @testing-library/svelte
vi.mock('@testing-library/svelte', () => {
	return {
		render: vi.fn(() => ({
			component: {},
			container: {
				querySelector: vi.fn(),
			},
			unmount: vi.fn(),
		})),
		screen: {
			getByRole: vi.fn(() => ({ getAttribute: vi.fn() })),
			getByLabelText: vi.fn(() => ({})),
			getByText: vi.fn(() => ({ getAttribute: vi.fn() })),
			queryByText: vi.fn(() => null),
		},
		waitFor: vi.fn((callback) => Promise.resolve(callback())),
		fireEvent: {
			click: vi.fn(),
			input: vi.fn(),
			submit: vi.fn(),
		},
	};
});

// Helper to "render" component with custom props (just returns mock)
export function renderWithProps(Component: any, props = {}) {
	// Instead of actually rendering, just return the component and props
	return {
		component: Component,
		props,
		container: {
			querySelector: vi.fn(),
		},
	};
}

// Clean up timers and mocks
beforeAll(() => {
	vi.useFakeTimers();
});

afterEach(() => {
	vi.resetAllMocks();
});

afterAll(() => {
	vi.useRealTimers();
});
