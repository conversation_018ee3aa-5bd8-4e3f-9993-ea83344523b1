<script lang="ts">
	import type { PageData } from './$types';
	export let data: PageData;
</script>

<div class="container mx-auto max-w-md py-16 text-center">
	{#if data.status === 'success'}
		<h1 class="mb-4 text-2xl font-semibold">Email Verified!</h1>
		<p class="mb-6">
			Thank you! Your email has been verified. You can now <a
				href="/auth/signin"
				class="text-blue-600 hover:underline">sign in</a
			>.
		</p>
	{:else if data.status === 'error'}
		<h1 class="mb-4 text-2xl font-semibold text-red-600">Verification Error</h1>
		<p class="mb-6">{data.message}</p>
	{:else}
		<h1 class="mb-4 text-2xl font-semibold text-red-600">Invalid Link</h1>
		<p class="mb-6">This verification link is missing or invalid.</p>
	{/if}
</div>
