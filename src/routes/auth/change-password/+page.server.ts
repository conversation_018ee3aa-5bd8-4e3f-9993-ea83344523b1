import { fail } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';
import { superValidate } from 'sveltekit-superforms/server';
import { zod } from 'sveltekit-superforms/adapters';
import { changePasswordSchema } from '$lib/schemas/auth';

export const load: PageServerLoad = async () => {
	const form = await superValidate(zod(changePasswordSchema));
	return { form };
};

export const actions: Actions = {
	default: async ({ request, locals }) => {
		const form = await superValidate(request, zod(changePasswordSchema));
		if (!form.valid) {
			return fail(400, { form });
		}

		const { error } = await locals.supabase.auth.updateUser({
			password: form.data.password,
		});
		if (error) {
			form.errors.confirmPassword = [error.message];
			return fail(400, { form });
		}

		return { success: true };
	},
};
