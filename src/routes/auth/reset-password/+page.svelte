<script lang="ts">
	import type { PageData } from './$types';
	import { superForm } from 'sveltekit-superforms';
	import * as Form from '$lib/components/ui/form';
	import { Input } from '$lib/components/ui/input';
	import { toast } from 'svelte-sonner';

	const { data }: { data: PageData } = $props();
	const formHandler = superForm(data.form, {
		onResult: (event) => {
			if (event.result.type === 'success') {
				toast.info('Check your email for a password reset link');
			} else if (event.result.type === 'error') {
				toast.error('An error occurred while sending the reset link. Please try again.');
			}
		},
	});
	const { form, enhance } = formHandler;
</script>

<div class="container mx-auto max-w-md py-16">
	<h1 class="mb-6 text-2xl font-semibold">Reset Password</h1>
	<div class="rounded-lg border p-6 shadow-xs">
		<form method="POST" use:enhance>
			<Form.Field form={formHandler} name="email">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label>Email <span class="text-red-500">*</span></Form.Label>
						<Input {...props} type="email" placeholder="<EMAIL>" bind:value={$form.email} />
					{/snippet}
				</Form.Control>
				<Form.FieldErrors />
			</Form.Field>
			<div class="pt-6">
				<Form.Button class="w-full">Send Reset Link</Form.Button>
			</div>
		</form>
	</div>
</div>
