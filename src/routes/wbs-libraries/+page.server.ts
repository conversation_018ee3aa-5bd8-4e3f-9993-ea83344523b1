import { redirect } from 'sveltekit-flash-message/server';
import type { PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ locals, cookies }) => {
	// Check authentication
	if (!locals.user) {
		return redirect('/auth/signin', { type: 'error', message: 'Sign in to view.' }, cookies);
	}

	const { supabase } = locals;

	// Fetch all WBS libraries
	const { data: wbsLibraries, error: wbsLibrariesError } = await supabase
		.from('wbs_library')
		.select('*, wbs_library_item:wbs_library_item(count)')
		.order('name');

	if (wbsLibrariesError) {
		console.error('Error fetching WBS libraries:', wbsLibrariesError);
		return {
			wbsLibraries: [],
		};
	}

	// Process libraries to add item count
	const librariesWithCounts = wbsLibraries.map((library) => {
		const itemCount = library.wbs_library_item?.[0]?.count || 0;
		return {
			...library,
			itemCount,
		};
	});

	return {
		wbsLibraries: librariesWithCounts,
	};
};
