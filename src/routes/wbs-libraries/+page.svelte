<script lang="ts">
	import type { PageData } from './$types';
	import { Button } from '$lib/components/ui/button';

	let { data }: { data: PageData } = $props();
</script>

<div class="container max-w-(--breakpoint-xl) py-8">
	<div class="mb-8 flex items-center justify-between">
		<h1 class="text-3xl font-bold">Work Breakdown Structure Libraries</h1>
		<!-- Admin-only buttons would go here -->
	</div>

	{#if data.wbsLibraries.length === 0}
		<div class="rounded-lg border bg-slate-50 p-8 text-center">
			<h2 class="mb-2 text-xl font-semibold">No WBS Libraries Available</h2>
			<p class="mb-6 text-slate-600">No work breakdown structure libraries have been added yet.</p>
		</div>
	{:else}
		<div class="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
			{#each data.wbsLibraries as library (library.wbs_library_id)}
				<div
					class="overflow-hidden rounded-lg border bg-white shadow-xs transition-shadow hover:shadow-md"
				>
					<div class="p-6">
						<h2 class="mb-2 text-xl font-semibold">{library.name}</h2>
						{#if library.description}
							<p class="mb-4 text-slate-600">{library.description}</p>
						{:else}
							<p class="mb-4 text-slate-400 italic">No description provided</p>
						{/if}
						<div class="mt-4 flex items-center justify-between">
							<span class="text-sm text-slate-500">
								{library.itemCount}
								{library.itemCount === 1 ? 'item' : 'items'}
							</span>
							<a href="/wbs-libraries/{library.wbs_library_id}">
								<Button variant="outline" size="sm">View Library</Button>
							</a>
						</div>
					</div>
				</div>
			{/each}
		</div>
	{/if}
</div>
