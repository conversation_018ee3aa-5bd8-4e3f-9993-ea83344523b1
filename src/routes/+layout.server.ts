import type { LayoutServerLoad } from './$types';
import { loadFlash } from 'sveltekit-flash-message/server';
import { SIDEBAR_COOKIE_NAME } from '$lib/components/ui/sidebar/constants.js';

export const load: LayoutServerLoad = loadFlash(async ({ locals, depends, cookies }) => {
	depends('sidebar:clients');
	const { supabase } = locals;

	// Fetch all clients for the sidebar
	const { data: clients, error } = await supabase
		.from('client')
		.select('client_id, name, organization(name)')
		.order('name');
	if (error) {
		console.error('Error fetching clients for sidebar:', error);
	}

	const { session, user } = locals;

	const { data: profile, error: profileError } = user
		? await supabase.from('profile').select('*').eq('user_id', user.id).single()
		: { data: null, error: null };
	if (profileError) throw profileError;

	// Read sidebar state from cookie, default to false (closed)
	const sidebarCookie = cookies.get(SIDEBAR_COOKIE_NAME);
	const sidebarOpen = sidebarCookie === 'true';

	return {
		session,
		user,
		profile,
		cookies: cookies.getAll(),
		sidebarClients: clients || [],
		sidebarOpen,
	};
});
