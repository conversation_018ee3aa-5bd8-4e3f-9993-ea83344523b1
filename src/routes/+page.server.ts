import type { PageServerLoad } from './$types';

export const load = (async ({ locals }) => {
	// load the most recent three projects
	// TODO: This is relying on RLS to filter out projects the user doesn't have access to
	const projects = await locals.supabase
		.from('project')
		.select('*, client:client_id(name, organization(name))')
		.order('updated_at', { ascending: false })
		.limit(3);

	// TODO: This is relying on RLS to filter out clients the user doesn't have access to
	const clients = await locals.supabase
		.from('client')
		.select('*, organization(name)')
		.order('created_at', { ascending: false })
		.limit(3);

	return { projects: projects.data ?? [], clients: clients.data ?? [] };
}) satisfies PageServerLoad;
