<script lang="ts">
	import type { PageData } from './$types';
	import { superForm } from 'sveltekit-superforms';
	import { Button } from '$lib/components/ui/button';
	import { Input } from '$lib/components/ui/input';
	import { Label } from '$lib/components/ui/label';
	import { Textarea } from '$lib/components/ui/textarea';
	import {
		Card,
		CardContent,
		CardDescription,
		CardFooter,
		CardHeader,
		CardTitle,
	} from '$lib/components/ui/card';

	const { data }: { data: PageData } = $props();

	const {
		form: formData,
		enhance,
		errors,
		message,
	} = superForm(data.form, {
		resetForm: false,
		taintedMessage: false,
	});
</script>

<div class="container mx-auto max-w-2xl py-16">
	<h1 class="mb-6 text-3xl font-bold">Organization Settings</h1>

	<form method="POST" use:enhance>
		<Card>
			<CardHeader>
				<CardTitle>Organization Details</CardTitle>
				<CardDescription>Update your organization information</CardDescription>
			</CardHeader>
			<CardContent class="space-y-4">
				<div class="space-y-2">
					<Label for="name">Organization Name</Label>
					<Input id="name" name="name" bind:value={$formData.name} />
					{#if $errors.name}
						<p class="text-destructive text-sm">{$errors.name}</p>
					{/if}
				</div>

				<div class="space-y-2">
					<Label for="description">Description</Label>
					<Textarea id="description" name="description" bind:value={$formData.description} />
					{#if $errors.description}
						<p class="text-destructive text-sm">{$errors.description}</p>
					{/if}
				</div>

				<div class="space-y-2">
					<Label for="logo_url">Logo URL</Label>
					<Input id="logo_url" name="logo_url" bind:value={$formData.logo_url} />
					{#if $errors.logo_url}
						<p class="text-destructive text-sm">{$errors.logo_url}</p>
					{/if}
				</div>
			</CardContent>
			<CardFooter>
				<Button type="submit">Save Changes</Button>
			</CardFooter>
		</Card>
	</form>

	{#if $message}
		<div
			class="mt-4 rounded-md p-4 {$message.type === 'success'
				? 'bg-green-100 text-green-800'
				: 'bg-red-100 text-red-800'}"
		>
			{$message.text}
		</div>
	{/if}
</div>
