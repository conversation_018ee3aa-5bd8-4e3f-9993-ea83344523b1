import { fail } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';
import { message, superValidate } from 'sveltekit-superforms/server';
import { zod } from 'sveltekit-superforms/adapters';
import { inviteMemberSchema } from '$lib/schemas/organization';
import { redirect } from 'sveltekit-flash-message/server';

export const load: PageServerLoad = async () => {
	const form = await superValidate(zod(inviteMemberSchema));
	return { form };
};

export const actions: Actions = {
	default: async ({ request, locals, params, cookies, fetch }) => {
		// Invite a user to this organization
		// If it's a current user, add them to the organization_member table
		// If it's a new user, send them an invitation and add to invite table
		const form = await superValidate(request, zod(inviteMemberSchema));
		if (!form.valid) {
			return fail(400, { form });
		}

		const { supabase } = locals;

		const userId = locals.user?.id;

		if (!userId) {
			throw redirect('/auth/signin', { type: 'error', message: 'Sign in to view.' }, cookies);
		}

		const { data: org } = await supabase
			.rpc('get_organization_by_name', {
				org_name_param: params.org_name,
			})
			.maybeSingle();

		if (!org) {
			return redirect('/', { type: 'error', message: 'Organization not found.' }, cookies);
		}

		const { data: isAdmin, error } = await supabase.rpc('current_user_has_entity_role', {
			entity_type_param: 'organization',
			entity_id_param: org.org_id,
			min_role_param: 'admin',
		});

		if (error || !isAdmin) {
			return fail(403, {
				form,
				message:
					'You are not authorized to add members to this organization. Please check with the organization admin.',
			});
		}

		// Find user by email
		const { data: userProfile, error: findError } = await supabase
			.from('profile')
			.select('user_id')
			.eq('email', form.data.email)
			.maybeSingle();

		if (findError) {
			return message(form, { text: 'Something went wrong', type: 'error' });
		}

		if (!userProfile) {
			// User not found, send invitation
			const data = await fetch('/api/invites', {
				method: 'POST',
				body: JSON.stringify({
					resourceType: 'organization',
					resourceId: org.org_id,
					role: form.data.role,
					inviteeEmail: form.data.email,
					expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
				}),
			});
			if (!data.ok) {
				return message(form, {
					text: 'Failed to send invitation email. Please try again.',
					type: 'error',
				});
			} else {
				console.log('sent');
				return message(form, { text: 'Invitation sent', type: 'success' });
			}
		}

		// User found, add to organization
		const { error: memberError } = await supabase.from('membership').insert({
			entity_type: 'organization',
			entity_id: org.org_id,
			user_id: userProfile.user_id,
			role: form.data.role === 'member' ? 'viewer' : form.data.role,
		});
		if (memberError) {
			return message(form, { text: 'Something went wrong', type: 'error' });
		}

		// TODO: add user notification
		return message(form, { text: 'User added to organization', type: 'success' });
	},
};
