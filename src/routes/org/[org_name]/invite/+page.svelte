<script lang="ts">
	import type { PageData } from './$types';
	import { superForm } from 'sveltekit-superforms';
	import * as Form from '$lib/components/ui/form';
	import { Input } from '$lib/components/ui/input';
	import * as Select from '$lib/components/ui/select';
	import { capitalizeFirstLetter } from '$lib/utils';
	import { toast } from 'svelte-sonner';

	const { data }: { data: PageData } = $props();
	const form = superForm(data.form, {
		onUpdated: ({ form }) => {
			if (form.message) {
				if (form.message.type === 'success') {
					toast.success(form.message.text);
				} else if (form.message.type === 'error') {
					toast.error(form.message.text);
				}
			}
		},
	});
	const { form: formData, enhance } = form;
</script>

<div class="container mx-auto max-w-md py-16">
	<h1 class="mb-6 text-2xl font-semibold">Invite Member</h1>
	<div class="rounded-lg border p-6 shadow-xs">
		<form method="POST" use:enhance>
			<div class="space-y-4">
				<Form.Field {form} name="email">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>Email <span class="text-red-500">*</span></Form.Label>
							<Input
								{...props}
								type="email"
								placeholder="<EMAIL>"
								bind:value={$formData.email}
							/>
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>

				<Form.Field {form} name="role">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>Role <span class="text-red-500">*</span></Form.Label>
							<Select.Root type="single" bind:value={$formData.role} name={props.name}>
								<Select.Trigger {...props}>
									{$formData.role ? capitalizeFirstLetter($formData.role) : 'Select a role'}
								</Select.Trigger>
								<Select.Content>
									<Select.Item value="member" label="Member" />
									<Select.Item value="admin" label="Admin" />
								</Select.Content>
							</Select.Root>
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>

				<div class="pt-6">
					<Form.Button class="w-full">Invite</Form.Button>
				</div>
			</div>
		</form>
	</div>
</div>
