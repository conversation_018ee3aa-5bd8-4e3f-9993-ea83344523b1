import { redirect } from 'sveltekit-flash-message/server';
import type { PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ locals, params, cookies }) => {
	if (!locals.user) {
		return redirect('/auth/signin', { type: 'error', message: 'Sign in to view.' }, cookies);
	}

	const { supabase } = locals;
	const { org_name, client_name } = params;

	// Get organization by name
	const { data: organization, error: orgError } = await supabase
		.rpc('get_organization_by_name', { org_name_param: org_name })
		.maybeSingle();

	if (orgError || !organization) {
		return redirect('/', { type: 'error', message: 'Organization not found.' }, cookies);
	}

	// Fetch the client data
	const { data: client, error: clientError } = await supabase
		.from('client')
		.select('*, project(*)')
		.eq('name', client_name)
		.eq('org_id', organization.org_id)
		.limit(1)
		.maybeSingle();

	if (clientError || !client) {
		console.error('Error fetching client:', clientError);
		return redirect(
			`/org/${org_name}/clients`,
			{ type: 'error', message: 'Client not found.' },
			cookies,
		);
	}

	// Check if user has permission to manage team
	const { data: canManageTeam } = await supabase.rpc('is_client_admin', {
		client_id_param: client.client_id,
	});

	return {
		client,
		canManageTeam,
	};
};
