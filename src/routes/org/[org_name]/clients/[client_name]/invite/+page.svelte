<script lang="ts">
	import { Button } from '$lib/components/ui/button';
	import { Input } from '$lib/components/ui/input';
	import { superForm } from 'sveltekit-superforms';
	import type { PageData } from './$types';
	import { toast } from 'svelte-sonner';
	import { invalidate } from '$app/navigation';
	import * as Dialog from '$lib/components/ui/dialog';

	const { data }: { data: PageData } = $props();
	const { form: formData } = data;
	const client = $derived(data.client);
	const invites = $derived(data.invites);
	const members = $derived(data.members);

	// Initialize the form
	const { form, enhance, errors, constraints } = superForm(formData, {
		resetForm: true,
		onUpdated: ({ form }) => {
			if (form.message) {
				if (form.message.type === 'success') {
					toast.success(form.message.text);
				} else if (form.message.type === 'error') {
					toast.error(form.message.text);
				}
			}
		},
	});

	// Function to handle user removal
	// TODO: convert to superform
	async function removeUser(permissionId: number) {
		const formData = new FormData();
		formData.append('permissionId', permissionId.toString());

		await fetch(`?/remove`, {
			method: 'POST',
			body: formData,
		});

		invalidate('client:invite');
	}
</script>

<div class="container mx-auto max-w-2xl py-8">
	<div class="mb-6">
		<h1>Invite Team Members to {client.name}</h1>
		<p class="text-muted-foreground mt-1">
			Add team members to collaborate on this client's projects.
		</p>
	</div>

	<div class="@container flex flex-col gap-6">
		<!-- Invite Form Section -->
		<div class="rounded-lg border p-6">
			<h2 class="mb-4 text-xl font-semibold">Send Invitation</h2>
			<form method="POST" action="?/invite" use:enhance>
				<input type="hidden" name="client_id" value={client.client_id} />
				<div class="mb-4">
					<label for="email" class="mb-1 block text-sm font-medium">Email Address</label>
					<Input
						type="email"
						id="email"
						name="email"
						bind:value={$form.email}
						placeholder="<EMAIL>"
						class="w-full"
						{...$constraints.email}
					/>
					{#if $errors.email}
						<p class="mt-1 text-sm text-red-500">{$errors.email}</p>
					{/if}
				</div>

				<div class="mb-4">
					<label for="role" class="mb-1 block text-sm font-medium">Role</label>
					<div class="relative">
						<select
							id="role"
							name="role"
							bind:value={$form.role}
							class="w-full rounded-md border bg-transparent px-3 py-2 focus:border-blue-500 focus:ring-blue-500"
						>
							<option value="viewer">Viewer (can only view)</option>
							<option value="editor">Editor (can edit but not invite users)</option>
							<option value="admin">Admin (full access, can invite users)</option>
						</select>
					</div>
					{#if $errors.role}
						<p class="mt-1 text-sm text-red-500">{$errors.role}</p>
					{/if}
				</div>

				<div class="mt-4">
					<Button type="submit" class="w-full">Send Invitation</Button>
				</div>
			</form>
		</div>

		<!-- Team Members Section -->
		<div class="rounded-lg border">
			<h2 class="p-6 pb-4 text-xl font-semibold">Team Members</h2>
			{#if members.length + invites.length === 0}
				<div class="p-6 pt-0 text-center">
					<p class="text-muted-foreground">No team members yet.</p>
				</div>
			{:else}
				<div class="overflow-x-auto">
					<table class="w-full">
						<thead>
							<tr class="border-b">
								<th class="px-6 py-3 text-left font-medium">User</th>
								<th class="px-6 py-3 text-left font-medium">Role</th>
								<th class="px-6 py-3 text-left font-medium">Status</th>
								<th class="py-3 pr-9 pl-6 text-right font-medium">Actions</th>
							</tr>
						</thead>
						<tbody>
							{#each members as member (member.membership_id)}
								<tr class="hover:bg-muted/50 border-b last:border-b-0">
									<td class="px-6 py-3">
										<div>
											<span class="block font-medium">
												{member.full_name || member.email.split('@')[0]}
											</span>
											<span class="text-muted-foreground block text-sm">
												{member.email}
											</span>
										</div>
									</td>
									<td class="px-6 py-3">
										<span class="text-sm font-light capitalize">{member.roles.join(', ')}</span>
									</td>
									<td class="px-6 py-3">
										<span
											class="inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800"
										>
											Active
										</span>
									</td>
									{#if member.user_id !== data.user?.id}
										<td class="px-6 py-3 text-right">
											<Dialog.Root>
												<Dialog.Trigger>
													<Button variant="ghost" size="sm">Remove</Button>
												</Dialog.Trigger>
												<Dialog.Content class="sm:max-w-[425px]">
													<Dialog.Header>
														<Dialog.Title>Remove Team Member</Dialog.Title>
														<Dialog.Description>
															Are you sure you want to remove this team member from {client.name}?
														</Dialog.Description>
													</Dialog.Header>
													<div class="py-4">
														<p class="text-muted-foreground text-sm">
															This action will revoke {member.full_name ||
																member.email.split('@')[0]}'s access to this client and all its
															projects.
														</p>
													</div>
													<Dialog.Footer>
														<Dialog.Close>
															<Button variant="outline" class="mr-2">Cancel</Button>
														</Dialog.Close>
														<Button
															variant="destructive"
															onclick={() => removeUser(member.membership_id)}
														>
															Remove Member
														</Button>
													</Dialog.Footer>
												</Dialog.Content>
											</Dialog.Root>
										</td>
									{/if}
								</tr>
							{/each}
							{#each invites as invite (invite.invite_id)}
								<tr class="hover:bg-muted/50 border-b last:border-b-0">
									<td class="px-6 py-3">
										<div>
											{#if invite.invitee_email}
												<span class="block font-medium">
													{invite.invitee_email}
												</span>
												<span class="text-muted-foreground block text-sm">
													{invite.invitee_email}
												</span>
											{:else}
												<span class="block font-medium">{invite.invitee_email}</span>
												<span class="text-muted-foreground block text-sm">
													Pending registration
												</span>
											{/if}
										</div>
									</td>
									<td class="px-6 py-3">
										<span class="capitalize">{invite.role}</span>
									</td>
									<td class="px-6 py-3">
										<span
											class="inline-flex items-center rounded-full bg-yellow-100 px-2.5 py-0.5 text-xs font-medium text-yellow-800"
										>
											Pending
										</span>
									</td>
									<!-- TODO: implement disinvitation -->
									<!-- <td class="px-6 py-3 text-right">
										<Button
											variant="ghost"
											size="sm"
											onclick={() => isinvite(invite.invite_id)}
										>
											Remove
										</Button>
									</td> -->
								</tr>
							{/each}
						</tbody>
					</table>
				</div>
			{/if}
		</div>
	</div>
</div>
