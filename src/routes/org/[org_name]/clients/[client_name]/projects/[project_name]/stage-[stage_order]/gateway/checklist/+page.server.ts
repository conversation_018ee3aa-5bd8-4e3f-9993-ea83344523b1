import { superValidate, message } from 'sveltekit-superforms/server';
import { zod } from 'sveltekit-superforms/adapters';
import { z } from 'zod';
import { type Actions, fail } from '@sveltejs/kit';
import { getGatewayChecklistItems, upsertGatewayChecklistItem } from '$lib/project_utils';
import { redirect } from 'sveltekit-flash-message/server';
import { Constants } from '$lib/database.types';

// Create a type-safe array of valid checklist item statuses
const validStatuses = Constants.public.Enums.checklist_item_status;

// Base schema for checklist items
const checklistFormSchema = z.object({
	items: z.array(
		z.object({
			gateway_checklist_item_id: z.number(),
			name: z.string().min(1, 'Checklist item name is required'),
			description: z.string().optional().nullable(),
			status: z.enum(validStatuses),
		}),
	),
});

export async function load({ locals, params, cookies }) {
	const { supabase } = locals;
	const { client_name, project_name, org_name } = params;

	if (!client_name || !project_name) {
		return redirect(
			org_name ? `/org/${org_name}/clients` : '/',
			{ type: 'error', message: 'Not found' },
			cookies,
		);
	}

	// Get the project
	const { data: project, error: projectError } = await supabase
		.from('project')
		.select('*, client!inner(name), project_stage(*)')
		.eq('client.name', client_name)
		.eq('name', project_name)
		.limit(1)
		.maybeSingle();

	if (projectError || !project) {
		console.error('Error fetching project:', projectError);
		return redirect(
			`/org/${org_name}/clients/${client_name}`,
			{ type: 'error', message: 'Project not found' },
			cookies,
		);
	}

	// Find the current active stage (first incomplete stage)
	const currentStage =
		project.project_stage.find((stage) => !stage.date_completed) || project.project_stage?.[0];

	if (!currentStage) {
		return redirect(
			`/org/${org_name}/clients/${client_name}/projects/${project_name}`,
			{ type: 'error', message: 'No active project stage found' },
			cookies,
		);
	}

	// Check if user can edit the project
	const { data: canEditProject, error: canEditProjectError } = await supabase.rpc(
		'can_modify_project',
		{
			project_id_param: project.project_id,
		},
	);

	if (canEditProjectError) {
		console.error('Error checking project edit permissions:', canEditProjectError);
	}

	// Get checklist items for the current stage
	let checklistItems: Awaited<ReturnType<typeof getGatewayChecklistItems>> = [];
	try {
		checklistItems = await getGatewayChecklistItems(supabase, currentStage.project_stage_id);
	} catch (error) {
		console.error('Error fetching checklist items:', error);
	}

	// Prepare initial form data for checklist items
	const checklistFormData = {
		items: checklistItems.map((item) => ({
			gateway_checklist_item_id: item.gateway_checklist_item_id,
			name: item.name,
			description: item.description,
			status: item.status,
		})),
	};

	// Prepare form with initial data
	const checklistForm = await superValidate(checklistFormData, zod(checklistFormSchema));

	return {
		project,
		currentStage,
		checklistItems,
		canEditProject,
		checklistForm,
	};
}

export const actions: Actions = {
	// Update checklist items
	updateChecklist: async ({ request, locals, params, cookies }) => {
		const { supabase } = locals;
		const { client_name, project_name, org_name } = params;

		if (!client_name || !project_name) {
			return redirect(
				org_name ? `/org/${org_name}/clients` : '/',
				{ type: 'error', message: 'Not found' },
				cookies,
			);
		}

		// Get form data
		const form = await superValidate(request, zod(checklistFormSchema));
		if (!form.valid) {
			return fail(400, { form });
		}

		const { data: project } = await supabase
			.from('project')
			.select('*, client!inner(name)')
			.eq('client.name', client_name)
			.eq('name', project_name)
			.limit(1)
			.maybeSingle();

		if (!project) {
			return fail(404, { form, message: { type: 'error', text: 'Project not found' } });
		}

		// Check permissions
		const { data: canEdit } = await supabase.rpc('can_modify_project', {
			project_id_param: project.project_id,
		});

		if (!canEdit) {
			return fail(403, {
				form,
				message: { type: 'error', text: 'You do not have permission to edit this project' },
			});
		}

		// Get current project stage
		const { data: stages } = await supabase
			.from('project_stage')
			.select('*')
			.eq('project_id', project.project_id)
			.order('stage_order', { ascending: true });

		const currentStage = stages?.find((stage) => !stage.date_completed) || stages?.[0];

		if (!currentStage) {
			return fail(404, { form, message: { type: 'error', text: 'No active project stage found' } });
		}

		// Process each checklist item update
		for (const item of form.data.items) {
			// Use the upsertGatewayChecklistItem function to handle both item update and status change
			const { error, statusUpdated } = await upsertGatewayChecklistItem(
				supabase,
				{
					gateway_checklist_item_id: item.gateway_checklist_item_id,
					name: item.name,
					description: item.description,
					project_stage_id: currentStage.project_stage_id,
				},
				item.status,
			);

			if (error) {
				console.error('Error updating checklist item:', error);
				return fail(500, {
					form,
					message: { type: 'error', text: 'Failed to update checklist item' },
				});
			}

			if (!statusUpdated) {
				console.error('Status was not updated for item:', item.gateway_checklist_item_id);
			}
		}

		// Return success message
		return message(form, { type: 'success', text: 'Checklist items updated successfully' });
	},
};
