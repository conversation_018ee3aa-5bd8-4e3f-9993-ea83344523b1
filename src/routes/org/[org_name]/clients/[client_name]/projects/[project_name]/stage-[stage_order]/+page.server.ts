import { getGatewayChecklistItems } from '$lib/project_utils';
import { error, redirect } from '@sveltejs/kit';
import type { ServerLoad } from '@sveltejs/kit';

export const load: ServerLoad = async ({ params, locals }) => {
	const { supabase } = locals;
	const { client_name, project_name, org_name, stage_order } = params;

	if (!org_name) {
		throw redirect(303, '/');
	}

	// Get client
	const { data: clientData, error: clientError } = await supabase
		.from('client')
		.select('*, organization(name, org_id)')
		.eq('organization.name', org_name)
		.eq('name', client_name || '')
		.single();

	if (clientError) {
		console.error('Error fetching client:', clientError);
		throw redirect(303, `/org/${org_name}/clients`);
	}

	// Get project
	const { data: projectData, error: projectError } = await supabase
		.from('project')
		.select('*, client(organization(name, org_id))')
		.eq('client.organization.name', org_name)
		.eq('name', project_name || '')
		.eq('client_id', clientData.client_id)
		.single();

	if (projectError) {
		throw redirect(303, `/org/${org_name}/clients/${client_name}`);
	}

	// Get all project stages to determine accessibility
	const { data: allStages, error: allStagesError } = await supabase
		.from('project_stage')
		.select('*')
		.eq('project_id', projectData.project_id)
		.order('stage_order', { ascending: true });

	if (allStagesError) {
		throw error(500, 'Error fetching project stages');
	}

	// Get the requested stage
	const { data: stageData, error: stageError } = await supabase
		.from('project_stage')
		.select('*')
		.eq('project_id', projectData.project_id)
		.eq('stage_order', Number(stage_order))
		.single();

	if (stageError) {
		throw redirect(
			303,
			`/org/${org_name}/clients/${client_name}/projects/${project_name}/stage-manager`,
		);
	}

	// Find the current active stage (first incomplete stage)
	const currentStage = allStages?.find((stage) => !stage.date_completed) || allStages?.[0];

	// Check if this stage is accessible (completed stages or current stage)
	const isAccessible =
		stageData.date_completed || (currentStage && stageData.stage_order <= currentStage.stage_order);

	// Get gateway checklist items for this stage
	const checklistItems = await getGatewayChecklistItems(supabase, stageData.project_stage_id);

	// Calculate checklist completion percentage
	const totalItems = checklistItems?.length || 0;
	const completedItems = checklistItems?.filter((item) => item.status === 'Complete')?.length || 0;
	const completionPercentage = totalItems > 0 ? (completedItems / totalItems) * 100 : 0;

	return {
		client: clientData,
		project: projectData,
		stage: stageData,
		isAccessible,
		checklist: {
			items: checklistItems || [],
			total: totalItems,
			completed: completedItems,
			completionPercentage,
		},
	};
};
