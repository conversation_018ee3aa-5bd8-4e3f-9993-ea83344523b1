<script lang="ts">
	import type { PageData } from './$types';
	import { Button } from '$lib/components/ui/button';
	import { page } from '$app/state';

	const { data }: { data: PageData } = $props();
	const { client, project, stage, isAccessible } = data;

	// Function to get status label
	function getStatusLabel() {
		if (stage.date_completed) {
			return 'Completed';
		} else if (isAccessible) {
			return 'In Progress';
		} else {
			return 'Locked';
		}
	}

	// Function to get status color class
	function getStatusClass() {
		if (stage.date_completed) {
			return 'bg-green-100 text-green-800';
		} else if (isAccessible) {
			return 'bg-blue-100 text-blue-800';
		} else {
			return 'bg-gray-100 text-gray-800';
		}
	}
</script>

<div class="container mx-auto max-w-(--breakpoint-xl) py-8">
	<div class="mb-8">
		<div class="flex items-center justify-between">
			<h1 class="text-3xl font-bold">{stage.name}</h1>
			<span class="rounded-full px-3 py-1 text-sm font-medium {getStatusClass()}">
				{getStatusLabel()}
			</span>
		</div>
		<p class="mt-1 text-slate-600">Stage {stage.stage_order}</p>
	</div>

	<div class="grid grid-cols-1 gap-8 lg:grid-cols-3">
		<!-- Stage Details -->
		<div class="lg:col-span-2">
			<div class="rounded-lg border bg-white p-6 shadow-xs">
				<h2 class="mb-4 text-xl font-semibold">Stage Description</h2>
				{#if stage.description}
					<div class="prose max-w-none">
						<!-- eslint-disable-next-line svelte/no-at-html-tags -->
						<pre class="font-sans text-wrap">{stage.description || ''}</pre>
					</div>
				{:else}
					<p class="text-gray-600">No description available for this stage.</p>
				{/if}

				{#if stage.date_completed}
					<div class="mt-6 rounded-md bg-green-50 p-4">
						<div class="flex">
							<div class="shrink-0">
								<span class="text-green-600">✓</span>
							</div>
							<div class="ml-3">
								<h3 class="text-sm font-medium text-green-800">Stage Completed</h3>
								<div class="mt-2 text-sm text-green-700">
									<p>
										This stage was completed on {new Date(
											stage.date_completed,
										).toLocaleDateString()}
									</p>
								</div>
							</div>
						</div>
					</div>
				{/if}
			</div>
		</div>

		<!-- Stage Actions -->
		<div class="lg:col-span-1">
			<div class="rounded-lg border bg-white p-6 shadow-xs">
				<h2 class="mb-4 text-xl font-semibold">Stage Actions</h2>

				<div class="flex flex-col space-y-4">
					<a
						href="/org/{page.params
							.org_name}/clients/{client.name}/projects/{project.name}/stage-{stage.stage_order}/gateway"
					>
						<Button class="w-full" disabled={!isAccessible}>
							{stage.date_completed ? 'View Gateway' : 'Go to Gateway'}
						</Button>
					</a>

					<a
						href="/org/{page.params.org_name}/clients/{client.name}/projects/{project.name}/budget"
					>
						<Button variant="outline" class="w-full">View Budget for this Stage</Button>
					</a>
				</div>

				{#if !isAccessible}
					<div class="mt-6 rounded-md bg-yellow-50 p-4">
						<div class="flex">
							<div class="shrink-0">
								<span class="text-yellow-600">⚠</span>
							</div>
							<div class="ml-3">
								<h3 class="text-sm font-medium text-yellow-800">Stage Locked</h3>
								<div class="mt-2 text-sm text-yellow-700">
									<p>You must complete previous stages before accessing this one.</p>
								</div>
							</div>
						</div>
					</div>
				{/if}
			</div>
		</div>
	</div>
</div>
