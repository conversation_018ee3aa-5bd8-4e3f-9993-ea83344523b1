<script lang="ts">
	import { Button } from '$lib/components/ui/button';
	import { formatDate } from '$lib/utils';
	import type { PageData } from './$types';
	import { page } from '$app/state';

	const { data }: { data: PageData } = $props();
	const { clients, org_name } = data;
</script>

<div class="container mx-auto py-8">
	<div class="mb-6 flex items-start justify-between">
		<h1>Clients</h1>
		<Button href="/org/{page.params.org_name}/clients/new">Create Client</Button>
	</div>

	{#if clients.length === 0}
		<div class="rounded-lg border border-dashed p-8 text-center">
			<p class="text-muted-foreground">
				No clients found in {org_name}. Create your first client to get started.
			</p>
		</div>
	{:else}
		<div class="rounded-md border">
			<table class="w-full">
				<thead>
					<tr class="bg-muted/50 border-b">
						<th class="px-4 py-3 text-left font-medium">Name</th>
						<th class="px-4 py-3 text-left font-medium">Projects</th>
						<th class="px-4 py-3 text-left font-medium">Last Updated</th>
						<th class="px-4 py-3 text-center font-medium">Actions</th>
					</tr>
				</thead>
				<tbody>
					{#each clients as client (client.client_id)}
						<tr class="border-b">
							<td class="px-4 py-3">
								<a
									href={`/org/${page.params.org_name}/clients/${client.name}`}
									class="font-semibold hover:underline"
								>
									{client.name}
								</a>
							</td>
							<td class="px-4 py-3">
								{client.projectCount}
							</td>
							<td class="px-4 py-3">
								<date>{formatDate(client.updated_at)}</date>
							</td>
							<td class="px-4 py-3 text-center">
								{#if data.is_org_admin || client.is_client_admin}
									<Button
										href={`/org/${page.params.org_name}/clients/${client.name}/edit`}
										variant="outline"
										size="sm">Edit</Button
									>
								{/if}
							</td>
						</tr>
					{/each}
				</tbody>
			</table>
		</div>
	{/if}
</div>
