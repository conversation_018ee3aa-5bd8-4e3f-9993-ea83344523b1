import { redirect } from 'sveltekit-flash-message/server';
import type { PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ locals, params, cookies }) => {
	const { supabase } = locals;
	const { org_name } = params;

	const { data: org } = await supabase
		.rpc('get_organization_by_name', {
			org_name_param: org_name,
		})
		.maybeSingle();

	if (!org) {
		return redirect('/', { type: 'error', message: 'Organization not found.' }, cookies);
	}

	// Use the new RPC function to get clients with project count and permissions in one query
	const { data, error } = await supabase.rpc('get_clients_with_permissions', {
		org_name_param: org_name,
	});

	if (error) {
		console.error('Error fetching clients:', error);
		return redirect('/', { type: 'error', message: 'Error fetching clients.' }, cookies);
	}

	if (!data || data.length === 0) {
		return {
			clients: [],
			org_name,
			is_org_admin: false,
		};
	}

	// Transform the data to match the expected format
	const clients = data.map((client) => ({
		...client,
		projectCount: client.project_count,
	}));

	return {
		clients,
		org_name,
		is_org_admin: data.length > 0 ? data[0].is_org_admin : false,
	};
};
