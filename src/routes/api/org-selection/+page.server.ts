import { fail } from '@sveltejs/kit';
import { zod } from 'sveltekit-superforms/adapters';
import { superValidate } from 'sveltekit-superforms';
import { orgSelectionSchema } from '$lib/schemas/org-selection';
import { ORG_COOKIE_NAME, ORG_COOKIE_OPTIONS } from '$lib/current-org.svelte';
import type { Actions } from '@sveltejs/kit';

export const actions: Actions = {
	selectOrganization: async ({ request, cookies }) => {
		// Validate the form data using zod schema
		const form = await superValidate(request, zod(orgSelectionSchema));

		if (!form.valid) {
			return fail(400, { form });
		}

		const { orgId } = form.data;

		// Set the cookie with a 30-day expiry
		cookies.set(ORG_COOKIE_NAME, orgId, ORG_COOKIE_OPTIONS);

		// Return success for the enhance function to update the UI
		return { success: true, orgId };
	},
};
