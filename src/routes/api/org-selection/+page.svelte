<!-- This page just handles the form submission, it does not render any UI -->
<script lang="ts">
	import { goto } from '$app/navigation';
	import { onMount } from 'svelte';

	// Redirect back to where the user came from, or to the home page
	onMount(() => {
		const returnTo = sessionStorage.getItem('orgSwitcherReturnTo') || '/';
		sessionStorage.removeItem('orgSwitcherReturnTo');
		goto(returnTo);
	});
</script>

<div class="flex min-h-screen items-center justify-center">
	<p>Switching organization...</p>
</div>
