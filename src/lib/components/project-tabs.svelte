<script lang="ts">
	import * as DropdownMenu from '$lib/components/ui/dropdown-menu/index.js';
	import ListIcon from 'phosphor-svelte/lib/List';
	import type { Tables } from '$lib/database.types';
	import { useSidebar } from './ui/sidebar';
	import { page } from '$app/state';

	const sidebar = useSidebar();

	const {
		client_name,
		project_name,
		projectStages,
		currentStage,
	}: {
		client_name: string;
		project_name: string;
		projectStages: Tables<'project_stage'>[];
		currentStage: Tables<'project_stage'> | null;
	} = $props();

	const activeTabId = $derived.by(() => {
		const pathSegments = page.url.pathname.split('/');
		if (pathSegments.length > 2 && pathSegments[pathSegments.length - 2] === 'construction') {
			return `construction/${pathSegments[pathSegments.length - 1]}`;
		}
		if (pathSegments.length > 2 && pathSegments[pathSegments.length - 1] === 'gateway') {
			return `${pathSegments[pathSegments.length - 2]}/gateway`;
		}
		return pathSegments[pathSegments.length - 1];
	});

	type Tab = {
		id: string;
		name: string;
		always: boolean;
		accessible?: boolean;
		completed?: boolean;
		stage_order?: number;
	};

	// Function to check if a stage is accessible
	function isStageAccessible(stage: Tables<'project_stage'>): boolean {
		// Allow access to the current stage and all completed stages
		if (stage.date_completed || (currentStage && stage.stage_order <= currentStage.stage_order)) {
			return true;
		}
		return false;
	}

	// Generate tabs based on project stages
	const generateProjectTabs = (): Tab[] => {
		const mostImportantTabs: Tab[] = [
			{ id: 'overview', name: 'Overview', always: true },
			{ id: 'budget', name: 'Current Budget', always: true },
		];
		const endingTabs: Tab[] = [
			{ id: 'wbs', name: 'WBS', always: true },
			{ id: 'stage-manager', name: 'Stage Manager', always: false },
			{ id: 'team', name: 'Team', always: false },
			{ id: 'edit', name: 'Edit Project', always: false },
		];

		const stageTabs = [];

		if (projectStages && projectStages.length > 0) {
			for (const stage of projectStages) {
				// Add the stage tab
				const stageId = `stage-${stage.stage_order}`;
				const accessible = isStageAccessible(stage);

				stageTabs.push({
					id: stageId,
					name: stage.name,
					accessible,
					completed: !!stage.date_completed,
					stage_order: stage.stage_order,
					always: false,
				});

				// Add the gateway tab if the stage is active
				if (accessible && !stage.date_completed) {
					stageTabs.push({
						id: `stage-${stage.stage_order}/gateway`,
						name: `${stage.name} Gateway`,
						accessible,
						completed: !!stage.date_completed,
						stage_order: stage.stage_order,
						always: false,
					});
				}
			}
		}

		// Add construction-specific tabs if in construction stage
		const constructionStage = projectStages?.find((s) =>
			s.name.toLowerCase().includes('construction'),
		);
		if (constructionStage && isStageAccessible(constructionStage)) {
			mostImportantTabs.push(
				{ id: 'construction/risks', name: 'Risk Register', always: true },
				{ id: 'construction/pending', name: 'Pending Changes', always: true },
				{ id: 'construction/approved', name: 'Approved Changes', always: true },
				{ id: 'construction/cashflow', name: 'Cashflow', always: false },
				{ id: 'construction/schedule', name: 'Schedule', always: false },
			);
		}

		return [...mostImportantTabs, ...stageTabs, ...endingTabs];
	};

	// Generate all tabs
	const allTabs = $derived.by(generateProjectTabs);

	// Get visible tabs (always visible or accessible)
	const visibleTabs = $derived(
		allTabs.filter(
			(tab) => tab.always || (tab.accessible !== undefined && tab.accessible && !tab.completed),
		),
	);
	// Get hidden tabs (not always visible and not accessible)
	// const hiddenTabs = allTabs.filter(
	// 	(tab) => !tab.always && (!tab.accessible || tab.accessible === undefined),
	// );
</script>

<div
	id="project-tabs"
	class="fixed right-0 bottom-0 z-50 border-t border-gray-200 bg-white {sidebar.open
		? 'left-(--sidebar-width)'
		: 'left-(--sidebar-width-icon)'} transition-[left] duration-200 ease-linear"
>
	<div class="relative flex h-12 items-center">
		<DropdownMenu.Root>
			<DropdownMenu.Trigger>
				<div class="flex size-12 items-center justify-center">
					<ListIcon
						size={20}
						class="flex size-8 items-center justify-center transition-colors hover:bg-gray-100"
					/>
				</div>
			</DropdownMenu.Trigger>
			<DropdownMenu.Content align="start" alignOffset={4} sideOffset={4}>
				<DropdownMenu.Group>
					{#each allTabs as tab (tab.id)}
						<DropdownMenu.Item disabled={tab.accessible === false}>
							<a
								href="/org/{page.params
									.org_name}/clients/{client_name}/projects/{project_name}/{tab.id}"
								class="flex w-full items-center rounded-md px-3 py-2 {tab.id === activeTabId
									? 'text-primary bg-gray-600 font-medium'
									: 'text-gray-700 hover:bg-gray-100'} {tab.accessible === false
									? 'cursor-not-allowed opacity-50'
									: ''}"
								tabindex={tab.accessible === false ? -1 : 0}
							>
								{tab.name}
								{#if tab.completed}
									<span class="ml-2 text-green-600">✓</span>
								{/if}
							</a>
						</DropdownMenu.Item>
					{/each}
				</DropdownMenu.Group>
			</DropdownMenu.Content>
		</DropdownMenu.Root>

		<div class="hide-scrollbar flex overflow-x-auto">
			{#each visibleTabs as tab (tab.id)}
				<a
					href="/org/{page.params.org_name}/clients/{client_name}/projects/{project_name}/{tab.id}"
					class="flex h-12 items-center border-x border-gray-50 px-4 text-sm whitespace-nowrap text-gray-700 transition-colors {tab.id ===
					activeTabId
						? 'text-primary-foreground decoration-primary bg-gray-200 font-medium underline decoration-[3px] underline-offset-4'
						: 'bg-gray-50 hover:bg-gray-200'} {tab.accessible === false
						? 'cursor-not-allowed opacity-50'
						: ''} {tab.completed ? 'line-through' : ''}"
					tabindex={tab.accessible === false ? -1 : 0}
				>
					{tab.name}
					{#if tab.completed}
						<span class="ml-2 text-green-600">✓</span>
					{/if}
				</a>
			{/each}
		</div>
	</div>
</div>

<style>
	/* Hide scrollbar but keep functionality */
	.hide-scrollbar {
		-ms-overflow-style: none; /* Internet Explorer and Edge */
		scrollbar-width: none; /* Firefox */
	}

	.hide-scrollbar::-webkit-scrollbar {
		display: none; /* Chrome, Safari, and Opera */
	}
</style>
