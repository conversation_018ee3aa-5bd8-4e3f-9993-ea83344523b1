<script lang="ts">
	import * as Collapsible from '$lib/components/ui/collapsible/index.js';
	import * as Sidebar from '$lib/components/ui/sidebar/index.js';
	import CaretRightIcon from 'phosphor-svelte/lib/CaretRight';
	import type { IconComponentProps } from 'phosphor-svelte';
	import type { Component } from 'svelte';

	let {
		clients,
		items,
	}: {
		clients: { client_id: string; name: string; organization: { name: string } }[];
		items: {
			title: string;
			url: string;
			icon?: Component<IconComponentProps, object, ''>;
			isActive?: boolean;
			items?: {
				title: string;
				url: string;
			}[];
		}[];
	} = $props();

	const clientItems = $derived(
		clients.map((client) => ({
			title: client.name,
			url: `/org/${client.organization.name}/clients/${client.name}`,
		})),
	);
</script>

<Sidebar.Group>
	<Sidebar.Menu>
		{#each items as mainItem (mainItem.title)}
			<Collapsible.Root open={mainItem.isActive} class="group/collapsible">
				{#snippet child({ props })}
					<Sidebar.MenuItem {...props}>
						<div class="flex w-full">
							<a href={mainItem.url} class="grow">
								<Sidebar.MenuButton class="cursor-pointer">
									{#snippet tooltipContent()}
										{mainItem.title}
									{/snippet}
									{#if mainItem.icon}
										<mainItem.icon />
									{/if}
									<span>{mainItem.title}</span>
								</Sidebar.MenuButton>
							</a>
							<Collapsible.Trigger class="focus-visible:outline-primary ml-auto p-2">
								<CaretRightIcon
									class="transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90"
								/>
							</Collapsible.Trigger>
						</div>
						<Collapsible.Content>
							{#if mainItem.title === 'Clients'}
								<Sidebar.MenuSub>
									{#each clientItems as subItem (subItem.title)}
										<Sidebar.MenuSubItem>
											<Sidebar.MenuSubButton>
												{#snippet child({ props })}
													<a href={subItem.url} {...props}>
														<span>{subItem.title}</span>
													</a>
												{/snippet}
											</Sidebar.MenuSubButton>
										</Sidebar.MenuSubItem>
									{/each}
								</Sidebar.MenuSub>
							{/if}
							{#if mainItem.items}
								<Sidebar.MenuSub>
									{#each mainItem.items as subItem (subItem.title)}
										<Sidebar.MenuSubItem>
											<Sidebar.MenuSubButton>
												{#snippet child({ props })}
													<a href={subItem.url} {...props}>
														<span>{subItem.title}</span>
													</a>
												{/snippet}
											</Sidebar.MenuSubButton>
										</Sidebar.MenuSubItem>
									{/each}
								</Sidebar.MenuSub>
							{/if}
						</Collapsible.Content>
					</Sidebar.MenuItem>
				{/snippet}
			</Collapsible.Root>
		{/each}
	</Sidebar.Menu>
</Sidebar.Group>
