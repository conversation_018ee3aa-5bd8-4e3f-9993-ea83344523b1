import type { Enums } from '$lib/database.types';
import { z } from 'zod';

export const wbsLibraryRoles = ['viewer', 'editor', 'owner'] as const;

// Schema for WBS libraries
export const wbsLibrarySchema = z.object({
	name: z.string().min(1, 'Library name is required'),
	description: z.string().optional().nullable(),
});

export type WbsLibrary = z.infer<typeof wbsLibrarySchema>;

export const wbsItemTypes = ['Standard', 'Custom'] as const;
export type WbsItemType = Enums<'wbs_item_type'>;

// Schema for WBS library items (both standard and custom)
const wbsLibraryItemSchemaBase = z.object({
	wbs_library_id: z.number(),
	code: z.string().min(1, 'Code is required'),
	in_level_code: z.string().min(1, 'In-level code is required'),
	description: z.string(),
	cost_scope: z.string().optional().nullable(),
	parent_item_id: z.number().optional().nullable(),
	level: z.number().positive(),
	item_type: z.enum(wbsItemTypes).default('Custom'),
	// Only required for custom items
	client_id: z.string().uuid().optional().nullable(),
	project_id: z.string().uuid().optional().nullable(),
});

export const wbsLibraryItemSchema = wbsLibraryItemSchemaBase
	.refine((data) => (data.parent_item_id ? data.level > 1 : data.level === 1), {
		message: 'Parent is required for level 2 and above',
		path: ['parent_item_id'],
	})
	.refine((data) => data.item_type !== 'Custom' || data.client_id !== null, {
		message: 'Client ID is required for custom items',
		path: ['client_id'],
	});

export type WbsLibraryItem = z.infer<typeof wbsLibraryItemSchema>;

// Schema for WBS library items with ID
export const wbsLibraryItemWithIdSchema = wbsLibraryItemSchemaBase
	.extend({
		wbs_library_item_id: z.number(),
	})
	.refine((data) => (data.parent_item_id ? data.level > 1 : data.level === 1), {
		message: 'Parent is required for level 2 and above',
		path: ['parent_item_id'],
	})
	.refine((data) => data.item_type !== 'Custom' || data.client_id !== null, {
		message: 'Client ID is required for custom items',
		path: ['client_id'],
	});

export type WbsLibraryItemWithId = z.infer<typeof wbsLibraryItemWithIdSchema>;

// Define the tree types with recursive structures
export interface WbsItemTree extends WbsLibraryItemWithId {
	children: WbsItemTree[];
	totalCost?: number;
}
