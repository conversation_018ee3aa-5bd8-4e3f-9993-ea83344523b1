import { z } from 'zod';

export const organizationSchema = z.object({
	name: z.string().min(1, { message: 'Name is required' }),
	description: z.string().optional(),
	logo_url: z.string().url({ message: 'Invalid URL' }).optional(),
});

export const inviteMemberSchema = z.object({
	email: z.string().email({ message: 'Invalid email address' }),
	role: z.enum(['member', 'admin'], { message: 'Role is required' }),
});
