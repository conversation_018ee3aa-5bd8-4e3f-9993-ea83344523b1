import { RESEND_API_KEY } from '$env/static/private';
import { PUBLIC_FROM_EMAIL_ADDRESS } from '$env/static/public';
import { Resend } from 'resend';

const resend = new Resend(RESEND_API_KEY);

const FROM = PUBLIC_FROM_EMAIL_ADDRESS;
const REPLY_TO = FROM;

// TODO: add this to a queue so that we can send emails in the background
// and not block the request
export const sendEmail = async ({
	to,
	subject,
	text,
	html,
}: {
	to: string;
	subject: string;
	text?: string;
	html?: string;
}) => {
	if (!to) {
		throw new Error('No recipient provided');
	}
	// Send an email
	// https://resend.com/docs/send-email
	if (html) {
		const { data, error } = await resend.emails.send({
			from: FROM,
			to,
			replyTo: REPLY_TO,
			subject,
			html,
		});
		if (error) {
			console.error('Error sending email:', error);
			return { error };
		}
		console.log('Email sent', { data });
		return { success: true };
	}
	if (text) {
		const { data, error } = await resend.emails.send({
			from: FROM,
			to,
			replyTo: REPLY_TO,
			subject,
			text,
		});
		if (error) {
			console.error('Error sending email:', error);
			return { error };
		}
		console.log('Email sent', { data });
		return { success: true };
	}
	return { error: new Error('Either text or html must be provided') };
};
