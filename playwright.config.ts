import { defineConfig, devices } from '@playwright/test';

export default defineConfig({
	webServer: {
		command: 'npm run dev',
		port: 5173,
		reuseExistingServer: true,
	},

	testDir: 'e2e',

	// Configure projects for major browsers
	projects: [
		{
			name: 'chromium',
			use: { ...devices['Desktop Chrome'] },
		},
	],

	// Global test settings
	use: {
		// Base URL for your application
		baseURL: 'http://localhost:5173',

		// Browser settings optimized for MCP
		headless: true,
		viewport: { width: 1280, height: 720 },
		ignoreHTTPSErrors: true,

		// Screenshot and video settings
		screenshot: 'only-on-failure',
		video: 'retain-on-failure',

		// Timeout settings
		actionTimeout: 10000,
		navigationTimeout: 30000,
	},

	// Test timeout
	timeout: 30000,

	// Expect timeout for assertions
	expect: {
		timeout: 5000,
	},

	// Reporter configuration
	reporter: [['html'], ['list']],

	// Output directory for test results
	outputDir: 'test-results/',
});
