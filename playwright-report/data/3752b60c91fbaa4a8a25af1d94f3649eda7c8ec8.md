# Test info

- Name: Organization member invitation >> should validate form inputs
- Location: /Users/<USER>/Projects/project-controls/e2e/organizations/invite-member.test.ts:61:2

# Error details

```
Error: expect.toBeVisible: Error: strict mode violation: locator('[data-fs-field-errors]') resolved to 2 elements:
    1) <div id="formsnap-5" aria-live="assertive" data-fs-field-errors="" class="text-destructive text-sm font-medium">…</div> aka locator('#formsnap-5')
    2) <div id="formsnap-10" aria-live="assertive" data-fs-field-errors="" class="text-destructive text-sm font-medium">…</div> aka locator('#formsnap-10')

Call log:
  - expect.toBeVisible with timeout 5000ms
  - waiting for locator('[data-fs-field-errors]')

    at /Users/<USER>/Projects/project-controls/e2e/organizations/invite-member.test.ts:66:56
```

# Page snapshot

```yaml
- region "Notifications alt+T"
- link:
  - /url: /
  - img
- separator
- list:
  - listitem:
    - link "Clients":
      - /url: /org/Test Org 1748365344140/clients
      - button "Clients":
        - img
        - text: Clients
    - button:
      - img
  - listitem:
    - link "WBS Libraries":
      - /url: /wbs-libraries
      - button "WBS Libraries":
        - img
        - text: WBS Libraries
    - button:
      - img
  - listitem:
    - link "Contractors":
      - /url: /contractors
      - button "Contractors":
        - img
        - text: Contractors
    - button:
      - img
- separator
- list:
  - listitem:
    - button "U"
- button "Toggle Sidebar"
- main:
  - button "Toggle Sidebar":
    - img
    - text: Toggle Sidebar
  - separator
  - navigation "breadcrumb":
    - list:
      - listitem:
        - link "Test Org 1748365344140":
          - /url: /org/Test Org 1748365344140
      - listitem:
        - link "Members":
          - /url: /org/Test Org 1748365344140/members
  - heading "Invite Member" [level=1]
  - text: Email *
  - textbox "Email *"
  - text: Invalid email address Role *
  - button "Role *":
    - text: Member
    - img
  - button "Invite"
- text: Cost Atlas
```

# Test source

```ts
   1 | import { test, expect } from '@playwright/test';
   2 |
   3 | test.describe('Organization member invitation', () => {
   4 | 	let orgName: string;
   5 | 	let orgNameEncoded: string;
   6 |
   7 | 	test.beforeEach(async ({ page }) => {
   8 | 		// Create a new user and organization for testing
   9 | 		const testEmail = `test-${Date.now()}@example.com`;
   10 | 		const testPassword = 'TestPassword123!';
   11 | 		orgName = `Test Org ${Date.now()}`;
   12 | 		orgNameEncoded = encodeURIComponent(orgName);
   13 |
   14 | 		// First, create a new user via signup
   15 | 		await page.goto('/auth/signup');
   16 | 		await page.fill('input[name="email"]', testEmail);
   17 | 		await page.fill('input[name="password"]', testPassword);
   18 | 		await page.click('button[type="submit"]');
   19 | 		await page.waitForLoadState('networkidle');
   20 |
   21 | 		// Login with the new user
   22 | 		await page.goto('/auth/signin');
   23 | 		await page.fill('input[name="email"]', testEmail);
   24 | 		await page.fill('input[name="password"]', testPassword);
   25 | 		await page.click('button[type="submit"]');
   26 | 		await page.waitForLoadState('networkidle');
   27 |
   28 | 		// User should be redirected to /org/new since they don't have an organization
   29 | 		await expect(page).toHaveURL(/\/org\/new/);
   30 |
   31 | 		// Create a new organization with unique name
   32 | 		await page.fill('input[name="name"]', orgName);
   33 | 		await page.fill('textarea[name="description"]', 'Test organization for e2e tests');
   34 | 		await page.click('button[type="submit"]');
   35 | 		await page.waitForLoadState('networkidle');
   36 |
   37 | 		// Should be redirected to the organization page (with URL encoded name)
   38 | 		await expect(page).toHaveURL(
   39 | 			new RegExp(`/org/${orgNameEncoded.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}`),
   40 | 		);
   41 |
   42 | 		// Navigate to the invite page
   43 | 		await page.goto(`/org/${orgNameEncoded}/invite`);
   44 | 		await expect(page).toHaveURL(
   45 | 			new RegExp(`/org/${orgNameEncoded.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}/invite`),
   46 | 		);
   47 | 	});
   48 |
   49 | 	test('should display the invite form', async ({ page }) => {
   50 | 		// Check form elements are present
   51 | 		await expect(page.locator('h1')).toContainText('Invite Member');
   52 | 		await expect(page.locator('form')).toBeVisible();
   53 | 		await expect(page.locator('input[type="email"]')).toBeVisible();
   54 | 		await expect(page.locator('button:has-text("Invite")')).toBeVisible();
   55 |
   56 | 		// Check that we have labels for the form fields
   57 | 		await expect(page.locator('text=Email')).toBeVisible();
   58 | 		await expect(page.locator('text=Role')).toBeVisible();
   59 | 	});
   60 |
   61 | 	test('should validate form inputs', async ({ page }) => {
   62 | 		// Try to submit with empty form
   63 | 		await page.click('button:has-text("Invite")');
   64 |
   65 | 		// Expect validation errors - check for form field errors
>  66 | 		await expect(page.locator('[data-fs-field-errors]')).toBeVisible();
      | 		                                                     ^ Error: expect.toBeVisible: Error: strict mode violation: locator('[data-fs-field-errors]') resolved to 2 elements:
   67 |
   68 | 		// Fill in invalid email
   69 | 		await page.fill('input[type="email"]', 'invalid-email');
   70 | 		await page.click('button:has-text("Invite")');
   71 |
   72 | 		// Expect email validation error
   73 | 		await expect(page.locator('[data-fs-field-errors]')).toBeVisible();
   74 | 	});
   75 |
   76 | 	test('should successfully invite a new member', async ({ page }) => {
   77 | 		// Fill the form with valid data
   78 | 		await page.fill('input[type="email"]', '<EMAIL>');
   79 |
   80 | 		// Click on the role dropdown and select 'Member'
   81 | 		await page.click('button:has-text("Select a role")');
   82 | 		await page.click('text=Member');
   83 |
   84 | 		// Submit the form
   85 | 		await page.click('button:has-text("Invite")');
   86 |
   87 | 		// Wait for form submission
   88 | 		await page.waitForLoadState('networkidle');
   89 |
   90 | 		// Expect success message via toast
   91 | 		await expect(page.locator('[data-sonner-toaster]')).toBeVisible({ timeout: 5000 });
   92 | 	});
   93 |
   94 | 	test('should show error if inviting existing member', async ({ page }) => {
   95 | 		// Fill the form with an email that's already a member (the current user)
   96 | 		// We'll use a different email since we can't easily get the current user's email
   97 | 		await page.fill('input[type="email"]', '<EMAIL>');
   98 |
   99 | 		// Click on the role dropdown and select 'Member'
  100 | 		await page.click('button:has-text("Select a role")');
  101 | 		await page.click('text=Member');
  102 |
  103 | 		// Submit the form
  104 | 		await page.click('button:has-text("Invite")');
  105 |
  106 | 		// For now, just check that the form submission works
  107 | 		// In a real scenario, we'd need to first add this user to test the error
  108 | 		await page.waitForLoadState('networkidle');
  109 | 	});
  110 |
  111 | 	test('should allow admin role selection', async ({ page }) => {
  112 | 		// Fill the form with valid data
  113 | 		await page.fill('input[type="email"]', '<EMAIL>');
  114 |
  115 | 		// Click on the role dropdown and select 'Admin'
  116 | 		await page.click('button:has-text("Select a role")');
  117 | 		await page.click('text=Admin');
  118 |
  119 | 		// Submit the form
  120 | 		await page.click('button:has-text("Invite")');
  121 |
  122 | 		// Wait for form submission
  123 | 		await page.waitForLoadState('networkidle');
  124 |
  125 | 		// Expect success message via toast
  126 | 		await expect(page.locator('[data-sonner-toaster]')).toBeVisible({ timeout: 5000 });
  127 | 	});
  128 | });
  129 |
```