import { defineConfig, devices } from '@playwright/test';

/**
 * Playwright configuration optimized for MCP (Model Context Protocol) usage
 * This configuration is specifically designed for AI-driven browser automation
 */
export default defineConfig({
	webServer: {
		command: 'npm run dev',
		port: 5173,
		reuseExistingServer: true,
	},

	testDir: 'e2e',

	// Configure projects for Chromium only (optimized for MCP)
	projects: [
		{
			name: 'chromium-mcp',
			use: { ...devices['Desktop Chrome'] },
		},
	],

	// Global test settings optimized for MCP
	use: {
		// Base URL for your application
		baseURL: 'http://localhost:5173',

		// Browser settings optimized for MCP automation
		headless: false, // Keep visible for MCP interaction
		viewport: { width: 1280, height: 720 },
		ignoreHTTPSErrors: true,

		// Screenshot and video settings for debugging
		screenshot: 'only-on-failure',
		video: 'retain-on-failure',
		trace: 'retain-on-failure',

		// Timeout settings - same as main config
		actionTimeout: 10000,
		navigationTimeout: 30000,

		// Locale and timezone
		locale: 'en-US',
		timezoneId: 'America/New_York',

		// Additional context options for MCP
		extraHTTPHeaders: {
			'Accept-Language': 'en-US,en;q=0.9',
		},
	},

	// Test timeout - same as main config
	timeout: 30000,

	// Expect timeout for assertions
	expect: {
		timeout: 5000,
	},

	// Reporter configuration
	reporter: [
		['html', { outputFolder: 'playwright-report-mcp' }],
		['list'],
		['json', { outputFile: 'test-results-mcp/results.json' }],
	],

	// Output directory for test results
	outputDir: 'test-results-mcp/',

	// Global setup and teardown
	globalSetup: undefined, // Add path to global setup file if needed
	globalTeardown: undefined, // Add path to global teardown file if needed

	// Test directory patterns
	testMatch: ['**/*.mcp.test.ts', '**/*-mcp.test.ts'],

	// Retry configuration
	retries: 2,

	// Worker configuration
	workers: 1, // Single worker for MCP to avoid conflicts

	// Fail fast on first failure
	maxFailures: 1,
});
