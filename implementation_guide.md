# Project Budget & Stage Management Implementation Guide

This document provides detailed specifications for the remaining implementation tasks for the Project Controls system. It's intended as a comprehensive guide for junior developers working on the project.

## Overview

We are building a hybrid budget tracking system with two key components:

1. **Frozen Snapshots**: Complete copies of the budget at stage gateways
2. **Incremental Audit Log**: Detailed tracking of every budget change between snapshots

The database schema and core utility functions are already implemented. Several UI components are also complete. This guide focuses on the remaining work needed to fully implement the system.

## 1. Construction Stage Features

### 1.1 Likely Changes Page

#### Purpose

To track potential changes to the budget that have not yet been approved but are likely to occur.

#### Database Tables (Already Created)

- `likely_changes` (needs to be created)
  - `likely_change_id` (PK)
  - `project_id` (FK)
  - `wbs_library_item_id` (FK)
  - `title`
  - `description`
  - `status` (enum: 'identified', 'submitted', 'pending approval')
  - `estimated_impact`
  - `created_by_user_id`
  - `created_at`
  - `updated_at`

#### Implementation Steps

1. Create new migration file for the `likely_changes` table including RLS policies
2. Create page at `/clients/[client_name]/projects/[project_name]/construction/likely-changes`
3. Implement UI with these key features:
   - Table view of all likely changes
   - Form to add/edit likely changes
   - Status filtering
   - Link each change to specific WBS items
   - Show total potential budget impact

#### UI Components

- Table with columns: Title, WBS Item, Status, Estimated Impact, Created Date, Actions
- Form with fields:
  - Title (text input)
  - WBS Item (dropdown)
  - Description (textarea)
  - Status (select)
  - Estimated Impact (number input)
- Status filter dropdown

#### Code Structure

- `+page.svelte`: Main UI components
- `+page.server.ts`: Data loading and form actions
- Use superForm for form handling
- Follow the same pattern as the Risks page

### 1.2 Approved Changes Page

#### Purpose

To track officially approved changes to the project budget.

#### Database Tables (Needs to be Created)

- `approved_changes`
  - `approved_change_id` (PK)
  - `project_id` (FK)
  - `wbs_library_item_id` (FK)
  - `title`
  - `description`
  - `approval_date`
  - `approved_by_user_id`
  - `amount`
  - `previous_budget_amount`
  - `new_budget_amount`
  - `created_at`
  - `updated_at`

#### Implementation Steps

1. Create new migration file for the `approved_changes` table including RLS policies
2. Create page at `/clients/[client_name]/projects/[project_name]/construction/approved-changes`
3. Implement UI with these key features:
   - Table view of all approved changes
   - Form to add/edit approved changes
   - Automatic calculation of new budget amounts
   - Integration with budget line items (updates should modify current budget)

#### UI Components

- Table with columns: Title, WBS Item, Approval Date, Amount, Previous Budget, New Budget, Actions
- Form with fields:
  - Title (text input)
  - WBS Item (dropdown)
  - Description (textarea)
  - Approval Date (date picker)
  - Amount (number input)
- Date range filter

#### Code Structure

- `+page.svelte`: Main UI components
- `+page.server.ts`: Data loading and form actions
- Function to automatically update the budget line item when an approved change is created

### 1.3 Cash Flow Schedule Component

#### Purpose

To visualize and manage the projected cash flow for the project over time.

#### Database Tables (Needs to be Created)

- `cash_flow_periods`

  - `cash_flow_period_id` (PK)
  - `project_id` (FK)
  - `period_name` (e.g., "Q1 2025")
  - `period_start_date`
  - `period_end_date`
  - `created_at`
  - `updated_at`

- `cash_flow_items`
  - `cash_flow_item_id` (PK)
  - `cash_flow_period_id` (FK)
  - `wbs_library_item_id` (FK)
  - `amount`
  - `created_at`
  - `updated_at`

#### Implementation Steps

1. Create new migration file for the cash flow tables including RLS policies
2. Create page at `/clients/[client_name]/projects/[project_name]/construction/cash-flow`
3. Implement UI with these key features:
   - Table view showing cash flow by period
   - Form to define periods
   - Interface to allocate budget items across periods
   - Chart visualization of cash flow over time

#### UI Components

- Period management section:
  - Table of defined periods
  - Form to add/edit periods
- Cash flow allocation section:
  - Matrix view with WBS items as rows and periods as columns
  - Input fields for allocating amounts per period
  - Auto-calculation of totals
- Visualization section:
  - Line chart showing cash flow over time
  - Bar chart showing period distributions

#### Code Structure

- `+page.svelte`: Main UI components
- `+page.server.ts`: Data loading and form actions
- Consider using a library like Chart.js for visualizations

## 2. Stage Management Enhancements

### 2.1 Stage Navigation with Gateway Restrictions

#### Purpose

To control the flow between project stages, ensuring that users cannot proceed to the next stage without completing the gateway requirements.

#### Implementation Steps

1. Modify the project-tabs component to dynamically generate tabs based on:
   - The current project stage
   - User permissions
   - Gateway completion status
2. Create helper function to determine available tabs
3. Implement navigation restrictions based on gateway checklist completion

#### UI Components

- Enhanced project-tabs component with conditional rendering
- Visual indicators for current stage and locked stages
- Info tooltips explaining why stages are locked

#### Code Structure

- Update `/src/lib/components/project-tabs.svelte`
- Create a new utility function in project_utils.ts:
  ```typescript
  function getAvailableTabs(
  	currentStage: ProjectStage,
  	checklistCompletion: boolean,
  	userPermissions: UserPermission,
  ): Tab[];
  ```

### 2.2 Stage Overview Dashboard

#### Purpose

To provide a high-level view of all project stages, their completion status, and gateway metrics.

#### Implementation Steps

1. Create page at `/clients/[client_name]/projects/[project_name]/stages`
2. Implement UI with these key features:
   - Timeline visualization of all stages
   - Summary of gateway checklist completion for each stage
   - Budget snapshot comparison across stages
   - Stage transition controls

#### UI Components

- Timeline component with stages
- Progress indicators for each stage
- Budget summary cards
- Gateway checklist summary
- Stage action buttons (complete, revert, etc.)

#### Code Structure

- `+page.svelte`: Main UI components
- `+page.server.ts`: Data loading and form actions
- Consider creating reusable components for stage cards

## 3. Testing and Performance Optimization

### 3.1 Test Cases for Budget Operations

#### Purpose

To ensure all budget functionality works correctly and maintains data integrity.

#### Test Cases

1. **Adding budget line items**:

   - Test with all fields populated
   - Test with minimal required fields
   - Verify computed costs match expected formula
   - Check for proper validation messages

2. **Updating budget line items**:

   - Test changing quantity and verifying cost recalculations
   - Test manual override functionality
   - Verify audit records are created correctly
   - Check timestamps and user tracking

3. **Manual cost overrides**:

   - Toggle override and verify behavior
   - Ensure calculated values are preserved
   - Test switching back from manual to calculated

4. **Audit trail verification**:
   - Make series of changes to a line item
   - Verify all changes are recorded in audit table
   - Check that old/new values are correctly captured
   - Verify user and timestamp information

### 3.2 Test Cases for Stage Gateway Operations

#### Purpose

To verify the stage progression and budget snapshot functionality.

#### Test Cases

1. **Gateway checklist management**:

   - Create various checklist items
   - Mark items as complete and verify state changes
   - Test "deferred" functionality
   - Verify gateway completion validation

2. **Stage completion**:

   - Test completing a stage with valid gateway
   - Verify budget snapshot is created
   - Check snapshot contents match current budget
   - Verify date_completed is set correctly

3. **Multiple stage progression**:
   - Progress through multiple stages
   - Verify snapshots are created at each gateway
   - Test snapshot comparison feature
   - Check budget evolution across stages

### 3.3 Performance Optimization

#### Purpose

To ensure the application remains responsive with large datasets.

#### Implementation Steps

1. Identify slow queries:

   - Add query logging in development
   - Test with large datasets (100+ budget items)

2. Optimize database indexes:

   - Review existing indexes
   - Add additional indexes based on common query patterns
   - Consider adding indexes on:
     - `budget_line_items_audit.change_timestamp`
     - `budget_snapshots.project_stage_id`

3. Optimize UI rendering:

   - Implement pagination for large datasets
   - Consider virtualized lists for very large tables
   - Use lazy loading for tab content

4. Query optimization:
   - Review and optimize complex joins
   - Consider adding materialized views for complex reports
   - Add database function for common calculations

## 4. Implementation Guidelines

### 4.1 Coding Standards

- Follow TypeScript best practices with strict type checking
- Use Svelte 5 conventions for reactivity (runes: `$state`, `$derived`, `$effect`)
- Use shadcn-svelte for UI components
- Follow Tailwind CSS conventions for styling
- Place all database access in server-side components

### 4.2 Error Handling

- Use try/catch blocks for all database operations
- Provide meaningful error messages to users
- Log detailed errors server-side
- Implement form validation with zod schemas
- Add appropriate fallbacks for missing data

### 4.3 Security Considerations

- Verify RLS policies for all new tables
- Ensure functions have appropriate SECURITY DEFINER attributes
- Validate all user inputs
- Check permissions before performing sensitive operations
- Never expose internal IDs in URLs when possible

### 4.4 Testing Approach

- Write unit tests for critical utility functions
- Create E2E tests for key user flows
- Test with diverse data sets
- Verify permissions work correctly
- Test edge cases and error conditions

## 5. Feature Implementation Priority

1. **Stage Navigation Restrictions** - High priority

   - This affects the core user flow through the application

2. **Approved Changes Module** - High priority

   - Critical for budget management during construction

3. **Likely Changes Module** - Medium priority

   - Important for risk management but can come after approved changes

4. **Cash Flow Schedule** - Medium priority

   - Important for financial planning but can be implemented later

5. **Performance Optimization** - Low priority initially
   - Focus on functionality first, then optimize as needed

## 6. Additional Resources

- Database schema diagrams in project documentation
- Existing component implementations for reference
- Svelte 5 documentation: https://svelte.dev/
- shadcn-svelte documentation: https://www.shadcn-svelte.com/
- Supabase PostgreSQL documentation: https://supabase.com/docs/guides/database
