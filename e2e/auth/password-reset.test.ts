import { expect, test } from '@playwright/test';

test.describe('Password Reset E2E Tests', () => {
	const testEmail = `test-${Date.now()}@example.com`;

	test('should display password reset request form', async ({ page }) => {
		// Navigate to the password reset page
		await page.goto('/auth/reset-password');

		// Verify page title
		await expect(page.locator('h1, h2').filter({ hasText: /reset\s+password/i })).toBeVisible();

		// Verify form elements
		await expect(page.getByLabel(/email/i)).toBeVisible();
		await expect(page.getByRole('button', { name: /reset/i })).toBeVisible();
	});

	test('should validate email format', async ({ page }) => {
		// Navigate to the password reset page
		await page.goto('/auth/reset-password');

		// Enter invalid email
		await page.getByLabel(/email/i).fill('invalid-email');

		// Submit the form
		await page.getByRole('button', { name: /send reset link/i }).click();

		// Should show validation error - check for form field errors (be more specific)
		await expect(page.locator('[data-fs-field-errors]')).toBeVisible({ timeout: 2000 });
	});

	test('should submit reset request for valid email', async ({ page }) => {
		// Navigate to the password reset page
		await page.goto('/auth/reset-password');

		// Enter valid email
		await page.getByLabel(/email/i).fill(testEmail);

		// Submit the form
		await page.getByRole('button', { name: /send reset link/i }).click();

		// Should show success message via toast - wait for the toast to appear
		await expect(page.locator('[data-sonner-toaster]')).toBeVisible({ timeout: 5000 });
		await expect(page.locator('text=/check your email/i')).toBeVisible({ timeout: 5000 });
	});

	// Note: Complete password reset flow cannot be fully automated without email integration
	// The following test simulates the password reset confirmation page
	test('should show password change form when using reset link', async ({ page }) => {
		// Simulate navigating to the password change page with a reset token
		// In a real scenario, this would come from clicking a link in an email
		// For testing, we can directly navigate to the page with a mock token
		await page.goto('/auth/change-password?token=mock-token');

		// Verify page has password fields
		await expect(page.getByLabel(/password/i).first()).toBeVisible();
		await expect(page.getByLabel(/confirm password/i)).toBeVisible();

		// Enter new password
		await page
			.getByLabel(/password/i)
			.first()
			.fill('NewPassword123');
		await page.getByLabel(/confirm password/i).fill('NewPassword123');

		// Submit the form
		await page
			.getByRole('button', { name: /change password|update password|save password/i })
			.click();

		// Since we're using a mock token, the form should be visible and functional
		// We don't expect an error message immediately, just that the form is present
		await expect(page.locator('h1').filter({ hasText: /change password/i })).toBeVisible();
	});

	test('should validate password requirements', async ({ page }) => {
		// Navigate to password change page
		await page.goto('/auth/change-password?token=mock-token');

		// Enter too short password
		await page.getByLabel(/new password/i).fill('short');
		await page.getByLabel(/confirm password/i).fill('short');

		// Submit the form
		await page.getByRole('button', { name: /change password/i }).click();

		// Should show password length validation error in form field errors
		await expect(page.locator('[data-fs-field-errors]')).toBeVisible({ timeout: 2000 });

		// Enter mismatched passwords
		await page.getByLabel(/new password/i).fill('NewPassword123');
		await page.getByLabel(/confirm password/i).fill('DifferentPassword123');

		// Submit the form
		await page.getByRole('button', { name: /change password/i }).click();

		// Should show password mismatch validation error in form field errors
		await expect(page.locator('[data-fs-field-errors]')).toBeVisible({ timeout: 2000 });
	});
});
