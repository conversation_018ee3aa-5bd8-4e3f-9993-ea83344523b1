import { expect, test } from '@playwright/test';

test.describe('Password Reset E2E Tests', () => {
	const testEmail = `test-${Date.now()}@example.com`;

	test('should display password reset request form', async ({ page }) => {
		// Navigate to the password reset page
		await page.goto('/auth/reset-password');

		// Verify page title
		await expect(page.locator('h1, h2').filter({ hasText: /reset\s+password/i })).toBeVisible();

		// Verify form elements
		await expect(page.getByLabel(/email/i)).toBeVisible();
		await expect(page.getByRole('button', { name: /reset/i })).toBeVisible();
	});

	test('should validate email format', async ({ page }) => {
		// Navigate to the password reset page
		await page.goto('/auth/reset-password');

		// Enter invalid email
		await page.getByLabel(/email/i).fill('invalid-email');

		// Submit the form
		await page.getByRole('button', { name: /reset/i }).click();

		// Should show validation error
		await expect(page.locator('text=Invalid email address')).toBeVisible({ timeout: 2000 });
	});

	test('should submit reset request for valid email', async ({ page }) => {
		// Navigate to the password reset page
		await page.goto('/auth/reset-password');

		// Enter valid email
		await page.getByLabel(/email/i).fill(testEmail);

		// Submit the form
		await page.getByRole('button', { name: /reset/i }).click();

		// Should show success message
		await expect(page.locator('text=/sent|check your email/')).toBeVisible({ timeout: 3000 });
	});

	// Note: Complete password reset flow cannot be fully automated without email integration
	// The following test simulates the password reset confirmation page
	test('should show password change form when using reset link', async ({ page }) => {
		// Simulate navigating to the password change page with a reset token
		// In a real scenario, this would come from clicking a link in an email
		// For testing, we can directly navigate to the page with a mock token
		await page.goto('/auth/change-password?token=mock-token');

		// Verify page has password fields
		await expect(page.getByLabel(/password/i).first()).toBeVisible();
		await expect(page.getByLabel(/confirm password/i)).toBeVisible();

		// Enter new password
		await page
			.getByLabel(/password/i)
			.first()
			.fill('NewPassword123');
		await page.getByLabel(/confirm password/i).fill('NewPassword123');

		// Submit the form
		await page
			.getByRole('button', { name: /change password|update password|save password/i })
			.click();

		// Note: In a real test, this would redirect to login page on success
		// Since we're using a mock token, we expect an error or redirection
		await expect(page.locator('text=/error|invalid token|success/i')).toBeVisible({
			timeout: 3000,
		});
	});

	test('should validate password requirements', async ({ page }) => {
		// Navigate to password change page
		await page.goto('/auth/change-password?token=mock-token');

		// Enter too short password
		await page
			.getByLabel(/password/i)
			.first()
			.fill('short');
		await page.getByLabel(/confirm password/i).fill('short');

		// Submit the form
		await page
			.getByRole('button', { name: /change password|update password|save password/i })
			.click();

		// Should show password length validation error
		await expect(page.locator('text=/at least 8 characters/i')).toBeVisible({ timeout: 2000 });

		// Enter mismatched passwords
		await page
			.getByLabel(/password/i)
			.first()
			.fill('NewPassword123');
		await page.getByLabel(/confirm password/i).fill('DifferentPassword123');

		// Submit the form
		await page
			.getByRole('button', { name: /change password|update password|save password/i })
			.click();

		// Should show password mismatch validation error
		await expect(page.locator("text=/passwords don't match|passwords must match/i")).toBeVisible({
			timeout: 2000,
		});
	});
});
