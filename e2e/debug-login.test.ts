import { test, expect } from '@playwright/test';

test.describe('Debug Login', () => {
	test('should debug login process', async ({ page }) => {
		// Navigate to signin page
		await page.goto('/auth/signin');
		
		// Check if we're on the signin page
		console.log('Current URL:', page.url());
		await expect(page).toHaveURL(/\/auth\/signin/);
		
		// Check if form elements are present
		const emailInput = page.locator('input[name="email"]');
		const passwordInput = page.locator('input[name="password"]');
		const submitButton = page.locator('button[type="submit"]');
		
		await expect(emailInput).toBeVisible();
		await expect(passwordInput).toBeVisible();
		await expect(submitButton).toBeVisible();
		
		// Fill in the form
		await emailInput.fill('<EMAIL>');
		await passwordInput.fill('password123');
		
		// Check if there are any validation errors before submitting
		const errorsBefore = await page.locator('[data-fs-field-errors]').count();
		console.log('Validation errors before submit:', errorsBefore);
		
		// Submit the form
		await submitButton.click();
		
		// Wait for any network activity to complete
		await page.waitForLoadState('networkidle');
		
		// Check current URL after submission
		console.log('URL after submit:', page.url());
		
		// Check if there are any validation errors after submitting
		const errorsAfter = await page.locator('[data-fs-field-errors]').count();
		console.log('Validation errors after submit:', errorsAfter);
		
		// If there are errors, log them
		if (errorsAfter > 0) {
			const errorTexts = await page.locator('[data-fs-field-errors]').allTextContents();
			console.log('Error messages:', errorTexts);
		}
		
		// Check if we're still on signin page or redirected
		const isStillOnSignin = page.url().includes('/auth/signin');
		console.log('Still on signin page:', isStillOnSignin);
		
		// If we're redirected, check where
		if (!isStillOnSignin) {
			console.log('Redirected to:', page.url());
		}
		
		// Check for any toast messages
		const toasts = await page.locator('[data-sonner-toaster]').count();
		console.log('Toast messages count:', toasts);
		
		if (toasts > 0) {
			const toastTexts = await page.locator('[data-sonner-toaster]').allTextContents();
			console.log('Toast messages:', toastTexts);
		}
	});
});
