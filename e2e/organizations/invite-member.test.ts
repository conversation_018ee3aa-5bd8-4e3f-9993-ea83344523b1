import { test, expect } from '@playwright/test';

test.describe('Organization member invitation', () => {
	test.beforeEach(async ({ page }) => {
		// Go to the homepage and login
		await page.goto('/auth/signin');
		await page.fill('input[name="email"]', '<EMAIL>');
		await page.fill('input[name="password"]', 'password123');
		await page.click('button[type="submit"]');

		// Wait for dashboard to load
		await page.waitForURL('**/api/org-selection');

		// Select the first organization (assuming test user has access)
		await page.click('a[href*="/organizations/"]');

		// Go to members page and then to invite page
		await page.click('a[href*="/members"]');
		await page.click('a[href*="/members/new"]');

		// Make sure we're on the invite page
		await expect(page).toHaveURL(/\/organizations\/.*\/members\/new/);
	});

	test('should display the invite form', async ({ page }) => {
		// Check form elements are present
		await expect(page.locator('h1')).toContainText('Invite New Member');
		await expect(page.locator('form')).toBeVisible();
		await expect(page.locator('input[type="email"]')).toBeVisible();
		await expect(page.locator('button:has-text("Send Invitation")')).toBeVisible();
		await expect(page.locator('button:has-text("Cancel")')).toBeVisible();
	});

	test('should validate form inputs', async ({ page }) => {
		// Try to submit with empty form
		await page.click('button:has-text("Send Invitation")');

		// Expect validation errors
		await expect(page.locator('text=Email is required')).toBeVisible();
		await expect(page.locator('text=Role is required')).toBeVisible();

		// Fill in invalid email
		await page.fill('input[type="email"]', 'invalid-email');
		await page.click('button:has-text("Send Invitation")');

		// Expect email validation error
		await expect(page.locator('text=Invalid email address')).toBeVisible();
	});

	test('should navigate back to members page on cancel', async ({ page }) => {
		// Click the cancel button
		await page.click('button:has-text("Cancel")');

		// Expect to be redirected to members page
		await expect(page).toHaveURL(/\/organizations\/.*\/members$/);
	});

	test('should successfully invite a new member', async ({ page }) => {
		// Fill the form with valid data
		await page.fill('input[type="email"]', '<EMAIL>');

		// Click on the role dropdown and select 'Member'
		await page.click('button:has-text("Select a role")');
		await page.click('div[role="option"]:has-text("Member")');

		// Submit the form
		await page.click('button:has-text("Send Invitation")');

		// Expect to be redirected to members page after successful submission
		await expect(page).toHaveURL(/\/organizations\/.*\/members$/);

		// Expect success message or indication
		await expect(page.locator('text=Invitation sent')).toBeVisible();
	});

	test('should show error if inviting existing member', async ({ page }) => {
		// Fill the form with an email of existing member
		await page.fill('input[type="email"]', '<EMAIL>');

		// Click on the role dropdown and select 'Member'
		await page.click('button:has-text("Select a role")');
		await page.click('div[role="option"]:has-text("Member")');

		// Submit the form
		await page.click('button:has-text("Send Invitation")');

		// Expect error message
		await expect(page.locator('text=User is already a member')).toBeVisible();
	});

	test('should allow admin role selection', async ({ page }) => {
		// Fill the form with valid data
		await page.fill('input[type="email"]', '<EMAIL>');

		// Click on the role dropdown and select 'Admin'
		await page.click('button:has-text("Select a role")');
		await page.click('div[role="option"]:has-text("Admin")');

		// Submit the form
		await page.click('button:has-text("Send Invitation")');

		// Expect to be redirected to members page after successful submission
		await expect(page).toHaveURL(/\/organizations\/.*\/members$/);
	});
});
