import { test, expect } from '@playwright/test';

test.describe('Organization member invitation', () => {
	test.beforeEach(async ({ page }) => {
		// Use the seeded test user from data.sql
		await page.goto('/auth/signin');
		await page.fill('input[name="email"]', '<EMAIL>');
		await page.fill('input[name="password"]', 'password123');
		await page.click('button[type="submit"]');

		// Wait for redirect and check where we end up
		await page.waitForLoadState('networkidle');

		// Debug: Check current URL
		console.log('Current URL after login:', page.url());

		// If we're not logged in, we'll be redirected to signup
		// Let's check if we're actually logged in by looking for a sign out button or user indicator
		const isLoggedIn = await page
			.locator('text=Sign out')
			.isVisible()
			.catch(() => false);
		console.log('Is logged in:', isLoggedIn);

		if (!isLoggedIn) {
			throw new Error('Login failed - user is not authenticated');
		}

		// Navigate directly to the Aurora organization's invite page using the new routing
		await page.goto('/org/Aurora/invite');

		// Make sure we're on the invite page
		await expect(page).toHaveURL(/\/org\/Aurora\/invite/);
	});

	test('should display the invite form', async ({ page }) => {
		// Check form elements are present
		await expect(page.locator('h1')).toContainText('Invite New Member');
		await expect(page.locator('form')).toBeVisible();
		await expect(page.locator('input[type="email"]')).toBeVisible();
		await expect(page.locator('button:has-text("Send Invitation")')).toBeVisible();
		await expect(page.locator('button:has-text("Cancel")')).toBeVisible();
	});

	test('should validate form inputs', async ({ page }) => {
		// Try to submit with empty form
		await page.click('button:has-text("Send Invitation")');

		// Expect validation errors
		await expect(page.locator('text=Email is required')).toBeVisible();
		await expect(page.locator('text=Role is required')).toBeVisible();

		// Fill in invalid email
		await page.fill('input[type="email"]', 'invalid-email');
		await page.click('button:has-text("Send Invitation")');

		// Expect email validation error
		await expect(page.locator('text=Invalid email address')).toBeVisible();
	});

	test('should navigate back to members page on cancel', async ({ page }) => {
		// Click the cancel button
		await page.click('button:has-text("Cancel")');

		// Expect to be redirected to members page using new routing
		await expect(page).toHaveURL(/\/org\/Aurora\/members$/);
	});

	test('should successfully invite a new member', async ({ page }) => {
		// Fill the form with valid data
		await page.fill('input[type="email"]', '<EMAIL>');

		// Click on the role dropdown and select 'member'
		await page.click('button:has-text("Select a role")');
		await page.click('div[role="option"]:has-text("member")');

		// Submit the form
		await page.click('button:has-text("Send Invitation")');

		// Expect to be redirected to members page after successful submission using new routing
		await expect(page).toHaveURL(/\/org\/Aurora\/members$/);

		// Expect success message via toast
		await expect(page.locator('[data-sonner-toast]')).toBeVisible({ timeout: 5000 });
	});

	test('should show error if inviting existing member', async ({ page }) => {
		// Fill the form with the test user's email (already a member)
		await page.fill('input[type="email"]', '<EMAIL>');

		// Click on the role dropdown and select 'member'
		await page.click('button:has-text("Select a role")');
		await page.click('div[role="option"]:has-text("member")');

		// Submit the form
		await page.click('button:has-text("Send Invitation")');

		// Expect error message via toast or form error
		await expect(page.locator('[data-sonner-toast]')).toBeVisible({ timeout: 5000 });
	});

	test('should allow admin role selection', async ({ page }) => {
		// Fill the form with valid data
		await page.fill('input[type="email"]', '<EMAIL>');

		// Click on the role dropdown and select 'admin'
		await page.click('button:has-text("Select a role")');
		await page.click('div[role="option"]:has-text("admin")');

		// Submit the form
		await page.click('button:has-text("Send Invitation")');

		// Expect to be redirected to members page after successful submission using new routing
		await expect(page).toHaveURL(/\/org\/Aurora\/members$/);
	});
});
