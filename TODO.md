# TODOs

## Done

    - [x] https://github.com/ciscoheat/sveltekit-flash-message
    - [x] https://superforms.rocks/flash-messages
    - [x] redirect to client page after creating client
    - [x] redirect to project page after creating project
    - [x] messages -> toasts in onUpdated https://superforms.rocks/concepts/messages
    - [x] decide on design for sidebar links to categories and implement it
    - [x] add clients to sidebar
    - [x] add new project
    - [x] edit new projects
    - [x] view WBS libraries
    - [x] view client-specific WBS libraries
    - [x] edit client-specific WBS libraries
    - [x] view project-specific WBS libraries
    - [x] edit project-specific WBS libraries
    - [x] remove custom WBS library table and use an isomorphic table
    - [x] edit client WBS new item form to add parent and put level and parent at the top
    - [x] create RIBA / project stage templates
    - [x] create route for project stage pages as param
    - [ ] tests tests tests
    - [x] update tables to use singular name for table name
    - [ ] transactional emails for invites
    - [ ] ~~| deeper integration of clerk auth with Supabase auth https://supabase.com/blog/clerk-tpa-pricing |~~
    - [x] rip out clerk auth and use Supabase auth
    	- [x] create sign up page
    	- [x] create sign in page
    	- [x] create password reset page
    	- [x] create password change page
    	- [x] create email verification page
    	- [x] create organization creation page
    	- [x] create user profile page
    	- [x] create organization invite page
    	- [x] create organization settings page
    	- [x] ~~| add organization to JWT with https://supabase.com/docs/guides/auth/auth-hooks/custom-access-token-hook |~~
    	- [x] create a toggle for current organization with a $state rune and filter queries (only needed for client queries) based on that organization
    - [x] settings page

## Future

- [ ] make editing budget simpler - inline editing like excel
- [ ] fix org/client/project membership model - see https://chatgpt.com/share/e/681b1957-21bc-8010-8a84-33efee52ea83
- [ ] work on overview page

- [ ] add construction stage elements

  - [ ] risk register
  - [ ] pending change orders
  - [ ] approved change orders
  - [ ] overview page and dashboard overhaul
  - [ ] risk analysis and visualization

- [ ] schedule of WBS

  - [ ] cash flow schedule

- [ ] add tender stage elements
  - [ ] tender analysis
  - [ ] add contractor
  - [ ] add bid
  - [ ] align to WBS

## Polish

- [ ] store sidebar.open in local storage and use it to set the sidebar status on page load
- [ ] convert all Lucide icons to phosphor icons
