# Cline Rules

## Project Patterns
- this is a SvelteKit project for frontend and backend
- TypeScript for type safety
- <PERSON><PERSON><PERSON> and <PERSON>SLint for code formatting and linting
- a PostgreSQL database hosted on Supabase
- Clerk is used for authentication
- Supabase is also used for blob storage
- Z<PERSON> for schema validation; Use Zod for all data validation
- Formsnap and Sveltekit-Superforms with Svelte's form actions and use:enhance for form handling
- Implement form handling with Formsnap and Superforms with SvelteKit form actions
- TailwindCSS 4.0 for styling
- shadcn-svelte for base ui components where custom solutions are not needed
- Phosphor Icons for icons
- Vercel for deployment
- Vitest and <PERSON><PERSON> for testing

## SQL Patterns
- Follow SQL style guide in ./cline/sqlStyleGuide.md for consistent formatting
- Use snake_case for table and column names
- Include schema in all SQL queries
- Add comments for complex queries
- Use CTEs for complex queries
- Prefer performance-optimized RLS policies and follow style guide in ./cline/rlsStyleGuide.md

## Development Workflow
- Validate RLS policies thoroughly
- Test database operations with multiple user contexts

## Corrections
- do not use import.meta.env for environment variables; use sveltekit's '$env/static/public' instead

## Commands

- Development: `pnpm dev` (or `npm run dev -- --open` to open browser)
- Build: `pnpm build`
- Preview: `pnpm preview`
- Lint and type check: `pnpm check`
- Unit Tests: `pnpm test:unit` (single test: `pnpm test:unit -- path/to/test.spec.ts`)
- E2E Tests: `pnpm test:e2e` (single test: `pnpm test:e2e -- path/to/test.test.ts`)
- Format: `pnpm format`
- Storybook: `pnpm storybook`

## Code Style

- **TypeScript**: Strict typing with no `any`. Do not be lazy! Do not use `any`!
- **Formatting**: Uses Prettier with 100 char line width, single quotes, tabs
- **Naming**: PascalCase for components, camelCase for variables/functions, snake_case for DB
- **Components**: Svelte 5 components with .svelte extension; use `const { data } = $props();` for props; do not use `$: ` for reactive statements, use $derived or $effect runes as appropriate.
- **Forms**: Always use shadcn-svelte components for forms and inputs with sveltekit-superforms and zod for validation; this is a MPA so use `import { superForm } from 'sveltekit-superforms';` and NOT: `import { superForm } from 'sveltekit-superforms/client';`
- **CRUD**: always use different routes for READ, CREATE, and UPDATE/DELETE CRUD operations. Update and delete can be combined and use named form actions
- **Imports**: Group by external/internal, alphabetize
- **Tailwind**: Prefer Tailwind classes over custom CSS
- **Error Handling**: Use try/catch with descriptive error messages
